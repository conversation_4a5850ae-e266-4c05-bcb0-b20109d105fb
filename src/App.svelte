<script lang="ts">
  import './app.css';
  import Navbar from './lib/components/Navbar.svelte';
  import Sidebar from './lib/components/Sidebar.svelte';

  import Dashboard from './lib/components/Dashboard.svelte';
  import About from './lib/components/About.svelte';
  import FAQ from './lib/pages/FAQ.svelte';
  import Privacy from './lib/pages/Privacy.svelte';
  import Blog from './lib/pages/Blog.svelte';
  import Blogs from './lib/pages/Blogs.svelte';
  import Feed from './lib/pages/Feed.svelte';
  import Flyers from './lib/pages/Flyers.svelte';
  import PriceHistory from './lib/pages/PriceHistory.svelte';
  import Landing from './lib/pages/Landing.svelte';
  import Footer from './lib/components/Footer.svelte';
  import FeedbackButton from './lib/components/FeedbackButton.svelte';
  import ResetPassword from './lib/pages/ResetPassword.svelte';
  import ForgotPassword from './lib/pages/ForgotPassword.svelte';
  import Auth from './lib/pages/Auth.svelte';
  import TermsOfService from './lib/pages/TermsOfService.svelte';
  import Community from './lib/pages/Community.svelte';
  import CommunityPost from './lib/pages/CommunityPost.svelte';
  import WebsiteTracking from './lib/pages/WebsiteTracking.svelte';
  import Unsubscribe from './lib/pages/Unsubscribe.svelte';
  import { user } from './lib/stores/auth';
  import { page } from './lib/stores/navigation';
  
  $: currentPage = $page.startsWith('blog/') ? 'blog' : $page.startsWith('blogs/') ? 'blogs' : $page.startsWith('community/') ? 'community-post' : $page.startsWith('flyers/') ? 'flyers' : $page;
  $: blogSlug = $page.startsWith('blogs/') ? $page.slice(6) : undefined;
  $: postId = $page.startsWith('community/') ? $page.slice(10) : undefined;
</script>

{#if $user}
  <!-- Sidebar for authenticated users -->
  <Sidebar />
  <!-- Main content with sidebar offset on desktop -->
  <main class="lg:ml-64">
    {#if currentPage === 'dashboard'}
      <Dashboard />
    {:else if currentPage === 'price-history'}
      <PriceHistory />
    {:else if currentPage === 'website-tracking'}
      <WebsiteTracking />
    {:else if currentPage === 'feed'}
      <Feed />
    {:else if currentPage === 'flyers'}
      <Flyers />
    {:else if currentPage === 'about'}
      <About />
    {:else if currentPage === 'faq'}
      <FAQ />
    {:else if currentPage === 'privacy'}
      <Privacy />
    {:else if currentPage === 'terms'}
      <TermsOfService />
    {:else if currentPage === 'blog'}
      <Blogs />
    {:else if currentPage === 'blogs'}
      <Blog slug={blogSlug} />
    {:else if currentPage === 'community'}
      <Community />
    {:else if currentPage === 'community-post'}
      <CommunityPost />
    {:else if currentPage === 'reset-password'}
      <ResetPassword />
    {:else if currentPage === 'forgot-password'}
      <ForgotPassword />
    {:else if currentPage === 'auth'}
      <Auth />
    {:else if currentPage === 'unsubscribe'}
      <Unsubscribe />
    {:else if currentPage === 'home'}
      <Landing />
    {:else}
      <Dashboard />
    {/if}
  </main>
{:else}
  <!-- Navbar for non-authenticated users -->
  <Navbar />
  <main>
    {#if currentPage === 'feed'}
      <Feed />
    {:else if currentPage === 'flyers'}
      <Flyers />
    {:else if currentPage === 'about'}
      <About />
    {:else if currentPage === 'faq'}
      <FAQ />
    {:else if currentPage === 'privacy'}
      <Privacy />
    {:else if currentPage === 'terms'}
      <TermsOfService />
    {:else if currentPage === 'blog'}
      <Blogs />
    {:else if currentPage === 'blogs'}
      <Blog slug={blogSlug} />
    {:else if currentPage === 'community'}
      <Community />
    {:else if currentPage === 'community-post'}
      <CommunityPost />
    {:else if currentPage === 'forgot-password'}
      <ForgotPassword />
    {:else if currentPage === 'auth'}
      <Auth />
    {:else if currentPage === 'unsubscribe'}
      <Unsubscribe />
    {:else}
      <Landing />
    {/if}
  </main>
{/if}

<FeedbackButton />
<div class="{$user ? 'lg:ml-64' : ''}">
  <Footer />
</div>

<style>
  :global(body) {
    margin: 0;
    padding: 0;
  }
</style>