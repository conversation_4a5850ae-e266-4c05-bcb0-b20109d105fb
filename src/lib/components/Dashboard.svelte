<script lang="ts">
  import { onMount } from 'svelte';
  import ProductGrid from './product/ProductGrid.svelte';
  import ReceiptSection from './receipt/ReceiptSection.svelte';
  import AddProductOptionsModal from './product/AddProductOptionsModal.svelte';
  import AddProductModal from './product/AddProductModal.svelte';
  import AddByItemNumberModal from './product/AddByItemNumberModal.svelte';
  import AddReceiptModal from './product/AddReceiptModal.svelte';
  import ProductLimitModal from './product/ProductLimitModal.svelte';
  import { getProducts, deleteProduct } from '../stores/products';
  import { CircleAlert as AlertCircle } from 'lucide-svelte';
  
  let products = [];
  let loading = true;
  let error = '';
  let showOptionsModal = false;
  let showUrlModal = false;
  let showItemNumberModal = false;
  let showReceiptModal = false;
  let refreshTrigger = 0; // Add this to trigger receipt section refresh
  let showProductLimitModal = false;

  // Product limit check
  $: isAtProductLimit = products.length >= 5;

  function handleAddProductClick() {
    if (isAtProductLimit) {
      showProductLimitModal = true;
    } else {
      showOptionsModal = true;
    }
  }

  async function loadProducts() {
    try {
      loading = true;
      error = '';
      products = await getProducts();
    } catch (e) {
      error = e.message;
    } finally {
      loading = false;
    }
  }

  async function handleDelete(id: string) {
    try {
      await deleteProduct(id);
      products = products.filter(p => p.id !== id);
    } catch (e) {
      error = 'Failed to delete product. Please try again.';
    }
  }

  function handleProductSuccess() {
    showUrlModal = false;
    showItemNumberModal = false;
    showReceiptModal = false;
    loadProducts();
  }

  function handleReceiptSuccess() {
    showReceiptModal = false;
    refreshTrigger += 1; // Increment to trigger refresh
  }

  onMount(() => {
    loadProducts();

    // Listen for refresh events from ProductGrid
    const handleRefresh = () => {
      loadProducts();
    };

    window.addEventListener('refresh', handleRefresh);

    // Cleanup event listener
    return () => {
      window.removeEventListener('refresh', handleRefresh);
    };
  });
</script>

<svelte:head>
  <title>Your Price Tracking Dashboard | BargainHawk</title>
  <meta name="description" content="Track your products and get notified of price drops. Manage your price alerts and save money with automatic price adjustment notifications." />
  <meta name="robots" content="noindex, nofollow" />
</svelte:head>

<div class="min-h-screen gradient-bg">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Products Section -->
    <div class="flex items-center justify-between mb-8">
      <h1 class="text-3xl font-bold text-white">Your Tracked Products</h1>
      
      <button
        class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark
               transition-all duration-200 font-medium shadow-glow"
        on:click={handleAddProductClick}
      >
        Add Product
      </button>
    </div>

    {#if error}
      <div class="bg-red-900/20 border-l-4 border-red-500 p-4 mb-8 rounded-r-lg backdrop-blur-sm">
        <div class="flex items-center">
          <AlertCircle class="h-5 w-5 text-red-400 mr-2" />
          <p class="text-sm text-red-300">{error}</p>
        </div>
      </div>
    {/if}

    {#if loading}
      <div class="flex justify-center items-center min-h-[200px]">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    {:else if products.length === 0}
      <div class="text-center py-12 bg-dark-lighter/50 rounded-lg border border-gray-800/50 backdrop-blur-sm">
        <h2 class="text-xl font-medium text-white mb-2">No products tracked yet</h2>
        <p class="text-gray-300 mb-6">Start tracking products to get price drop notifications</p>
        <button
          class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg
                 hover:bg-primary-dark transition-all duration-200 font-medium shadow-glow"
          on:click={handleAddProductClick}
        >
          Add Your First Product
        </button>
      </div>
    {:else}
      <ProductGrid
        {products}
        onDelete={handleDelete}
        {isAtProductLimit}
        onAddClick={handleAddProductClick}
      />
    {/if}

    <!-- Receipts Section -->
    <div class="flex items-center justify-between mb-8">
      <h2 class="text-2xl font-bold text-white">Your Receipts</h2>

      <button
        class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark
               transition-all duration-200 font-medium shadow-glow"
        on:click={() => showReceiptModal = true}
      >
        Add New Receipts
      </button>
    </div>

    <ReceiptSection {refreshTrigger} />
  </div>
</div>

<AddProductOptionsModal
  show={showOptionsModal}
  on:close={() => showOptionsModal = false}

  on:addByUrl={() => {
    showOptionsModal = false;
    showUrlModal = true;
  }}
  on:addByItemNumber={() => {
    showOptionsModal = false;
    showItemNumberModal = true;
  }}
/>

<AddProductModal
  show={showUrlModal}
  on:close={() => showUrlModal = false}
  on:success={handleProductSuccess}
/>

<AddByItemNumberModal
  show={showItemNumberModal}
  on:close={() => showItemNumberModal = false}
  on:success={handleProductSuccess}
/>

<AddReceiptModal
  show={showReceiptModal}
  on:close={() => showReceiptModal = false}
  on:success={handleReceiptSuccess}
/>

<ProductLimitModal
  show={showProductLimitModal}
  on:close={() => showProductLimitModal = false}
/>