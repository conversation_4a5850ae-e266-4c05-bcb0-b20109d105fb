<script lang="ts">
  import { page } from '../stores/navigation';
</script>

<footer class="bg-gray-900 text-gray-400">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <div class="flex flex-col md:flex-row justify-between space-y-8 md:space-y-0">
      <!-- Left Column -->
      <div class="md:w-1/3">
        <h3 class="text-lg text-white font-semibold mb-4">BargainHawk</h3>
        <p class="text-sm max-w-xs">
          Track Costco prices automatically and never miss a price adjustment opportunity.
        </p>
      </div>

      <!-- Middle Column -->
      <div class="md:w-1/3 md:flex md:justify-center">
        <div>
          <h3 class="text-lg text-white font-semibold mb-4">Quick Links</h3>
          <ul class="space-y-2 text-sm">
            <li>
              <a
                href="/about"
                class="hover:text-white transition-colors"
                on:click={(e) => { e.preventDefault(); page.set('about'); }}
              >
                About
              </a>
            </li>
            <li>
              <a
                href="/faq"
                class="hover:text-white transition-colors"
                on:click={(e) => { e.preventDefault(); page.set('faq'); }}
              >
                FAQ
              </a>
            </li>
            <li>
              <a
                href="/blog"
                class="hover:text-white transition-colors"
                on:click={(e) => { e.preventDefault(); page.set('blog'); }}
              >
                Blog
              </a>
            </li>
            <li>
              <a
                href="/feed"
                class="hover:text-white transition-colors"
                on:click={(e) => { e.preventDefault(); page.set('feed'); }}
              >
                Price Drops
              </a>
            </li>
            <li>
              <a
                href="/community"
                class="hover:text-white transition-colors"
                on:click={(e) => { e.preventDefault(); page.set('community'); }}
              >
                Community
              </a>
            </li>
            <li>
              <a
                href="/privacy"
                class="hover:text-white transition-colors"
                on:click={(e) => { e.preventDefault(); page.set('privacy'); }}
              >
                Privacy Policy
              </a>
            </li>
            <li>
              <a
                href="/terms"
                class="hover:text-white transition-colors"
                on:click={(e) => { e.preventDefault(); page.set('terms'); }}
              >
                Terms of Service
              </a>
            </li>
          </ul>
        </div>
      </div>

      <!-- Right Column -->
      <div class="md:w-1/3 flex md:justify-end">
        <div>
          <h3 class="text-lg text-white font-semibold mb-4">Contact</h3>
          <p class="text-sm">
            Questions or feedback?<br />
            <a
              href="mailto:<EMAIL>"
              class="text-primary hover:text-secondary transition-colors"
            >
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>

    <!-- Copyright -->
    <div class="mt-12 pt-8 border-t border-gray-800 text-center text-sm">
      <p>
        &copy; {new Date().getFullYear()} 
        <a 
          href="https://maplecan.ca" 
          target="_blank" 
          rel="noopener noreferrer"
          class="text-primary hover:text-secondary transition-colors"
        >
          MapleCan Technologies Inc.
        </a> 
        All rights reserved.
      </p>
    </div>
  </div>
</footer>