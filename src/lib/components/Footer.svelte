<script lang="ts">
  import { page } from '../stores/navigation';
</script>

<footer class="bg-gray-900 text-gray-400">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <div class="flex flex-col md:flex-row justify-between space-y-8 md:space-y-0">
      <!-- Left Column -->
      <div class="md:w-1/3">
        <h3 class="text-lg text-white font-semibold mb-4">BargainHawk</h3>
        <p class="text-sm max-w-xs">
          Track Costco prices automatically and never miss a price adjustment opportunity.
        </p>
      </div>

      <!-- Middle Column -->
      <div class="md:w-1/3 md:flex md:justify-center">
        <div>
          <h3 class="text-lg text-white font-semibold mb-4">Quick Links</h3>
          <ul class="space-y-2 text-sm">
            <li>
              <a
                href="/about"
                class="hover:text-white transition-colors"
                on:click={(e) => { e.preventDefault(); page.set('about'); }}
              >
                About
              </a>
            </li>
            <li>
              <a
                href="/faq"
                class="hover:text-white transition-colors"
                on:click={(e) => { e.preventDefault(); page.set('faq'); }}
              >
                FAQ
              </a>
            </li>
            <li>
              <a
                href="/blog"
                class="hover:text-white transition-colors"
                on:click={(e) => { e.preventDefault(); page.set('blog'); }}
              >
                Blog
              </a>
            </li>
            <li>
              <a
                href="/feed"
                class="hover:text-white transition-colors"
                on:click={(e) => { e.preventDefault(); page.set('feed'); }}
              >
                Price Drops
              </a>
            </li>
            <li>
              <a
                href="/community"
                class="hover:text-white transition-colors"
                on:click={(e) => { e.preventDefault(); page.set('community'); }}
              >
                Community
              </a>
            </li>
            <li>
              <a
                href="/privacy"
                class="hover:text-white transition-colors"
                on:click={(e) => { e.preventDefault(); page.set('privacy'); }}
              >
                Privacy Policy
              </a>
            </li>
            <li>
              <a
                href="/terms"
                class="hover:text-white transition-colors"
                on:click={(e) => { e.preventDefault(); page.set('terms'); }}
              >
                Terms of Service
              </a>
            </li>
          </ul>
        </div>
      </div>

      <!-- Right Column -->
      <div class="md:w-1/3 flex md:justify-end">
        <div>
          <h3 class="text-lg text-white font-semibold mb-4">Contact</h3>
          <p class="text-sm mb-4">
            Questions or feedback?<br />
            <a
              href="mailto:<EMAIL>"
              class="text-primary hover:text-secondary transition-colors"
            >
              <EMAIL>
            </a>
          </p>

          <!-- Social Media Links -->
          <div class="flex space-x-4">
            <a
              href="https://www.instagram.com/bargainhawk/"
              target="_blank"
              rel="noopener noreferrer"
              class="text-gray-400 hover:text-white transition-colors"
              aria-label="Follow us on Instagram"
            >
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
              </svg>
            </a>

            <a
              href="https://www.reddit.com/r/bargainhawk/"
              target="_blank"
              rel="noopener noreferrer"
              class="text-gray-400 hover:text-white transition-colors"
              aria-label="Join our Reddit community"
            >
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0zm5.01 4.744c.688 0 1.25.561 1.25 1.249a1.25 1.25 0 0 1-2.498.056l-2.597-.547-.8 3.747c1.824.07 3.48.632 4.674 1.488.308-.309.73-.491 1.207-.491.968 0 1.754.786 1.754 1.754 0 .716-.435 1.333-1.01 1.614a3.111 3.111 0 0 1 .042.52c0 2.694-3.13 4.87-7.004 4.87-3.874 0-7.004-2.176-7.004-4.87 0-.183.015-.366.043-.534A1.748 1.748 0 0 1 4.028 12c0-.968.786-1.754 1.754-1.754.463 0 .898.196 1.207.49 1.207-.883 2.878-1.43 4.744-1.487l.885-4.182a.342.342 0 0 1 .14-.197.35.35 0 0 1 .238-.042l2.906.617a1.214 1.214 0 0 1 1.108-.701zM9.25 12C8.561 12 8 12.562 8 13.25c0 .687.561 1.248 1.25 1.248.687 0 1.248-.561 1.248-1.249 0-.688-.561-1.249-1.249-1.249zm5.5 0c-.687 0-1.248.561-1.248 1.25 0 .687.561 1.248 1.249 1.248.688 0 1.249-.561 1.249-1.249 0-.687-.562-1.249-1.25-1.249zm-5.466 3.99a.327.327 0 0 0-.231.094.33.33 0 0 0 0 .463c.842.842 2.484.913 2.961.913.477 0 2.105-.056 2.961-.913a.361.361 0 0 0 .029-.463.33.33 0 0 0-.464 0c-.547.533-1.684.73-2.512.73-.828 0-1.979-.196-2.512-.73a.326.326 0 0 0-.232-.095z"/>
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Copyright -->
    <div class="mt-12 pt-8 border-t border-gray-800 text-center text-sm">
      <p>
        &copy; {new Date().getFullYear()} 
        <a 
          href="https://maplecan.ca" 
          target="_blank" 
          rel="noopener noreferrer"
          class="text-primary hover:text-secondary transition-colors"
        >
          MapleCan Technologies Inc.
        </a> 
        All rights reserved.
      </p>
    </div>
  </div>
</footer>