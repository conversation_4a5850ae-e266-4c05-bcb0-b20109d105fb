<script lang="ts">
  export let href: string;
  export let external: boolean = false;
  export let customClass: string = '';
  
  import { page } from '../stores/navigation';
  
  function handleClick(event: MouseEvent) {
    if (external) {
      // Let external links work normally
      return;
    }
    
    event.preventDefault();
    
    // Handle internal navigation
    if (href === '/') {
      page.set('home');
    } else {
      // Remove leading slash and set page
      const pageName = href.startsWith('/') ? href.slice(1) : href;
      page.set(pageName);
    }
  }
  
  // Default classes for internal links
  const defaultClass = external ? '' : 'text-primary hover:text-primary-dark transition-colors';
  const finalClass = customClass || defaultClass;
</script>

<!-- 
  SEO-friendly link component that:
  1. Uses proper <a> tags with href attributes for SEO crawling
  2. Maintains SPA functionality by intercepting clicks
  3. Handles both internal and external links
  4. Provides proper fallback for users with JavaScript disabled
-->
<a
  {href}
  class={finalClass}
  on:click={handleClick}
  target={external ? '_blank' : undefined}
  rel={external ? 'noopener noreferrer' : undefined}
>
  <slot />
</a>
