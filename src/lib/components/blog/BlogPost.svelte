<script lang="ts">
  import type { BlogPost } from '../../data/blogPosts';
  import { formatDate } from '../../utils/format';
  import { page } from '../../stores/navigation';
  import { renderMarkdown } from '../../utils/markdown';
  
  export let post: BlogPost;
  
  $: renderedContent = renderMarkdown(post.content);
  $: schemaString = JSON.stringify({
    "@context": "https://schema.org",
    "@type": "BlogPosting",
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `https://bargainhawk.ca/blog/${post.slug}`
    },
    "headline": post.title,
    "description": post.description,
    "image": {
      "@type": "ImageObject",
      "url": "https://bargainhawk.ca/logo.png",
      "width": 512,
      "height": 512
    },
    "keywords": post.keywords.join(', '),
    "datePublished": post.date,
    "dateModified": post.date,
    "author": {
      "@type": "Person",
      "name": post.author
    },
    "publisher": {
      "@type": "Organization",
      "name": "BargainHawk",
      "logo": {
        "@type": "ImageObject",
        "url": "https://bargainhawk.ca/logo.png",
        "width": 512,
        "height": 512
      }
    }
  });
</script>

<svelte:head>
  <title>{post.title} | BargainHawk Blog</title>
  <meta name="description" content={post.description} />
  <meta name="keywords" content={post.keywords.join(', ')} />
  <link rel="canonical" href="https://bargainhawk.ca/blog/{post.slug}" />
  
  <!-- Enhanced Meta Tags for Search Engines -->
  <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
  
  <!-- Open Graph Tags -->
  <meta property="og:title" content={post.title} />
  <meta property="og:description" content={post.description} />
  <meta property="og:type" content="article" />
  <meta property="og:url" content="https://bargainhawk.ca/blog/{post.slug}" />
  <meta property="og:site_name" content="BargainHawk" />
  <meta property="og:image" content="https://bargainhawk.ca/logo.png" />
  <meta property="article:published_time" content={post.date} />
  <meta property="article:modified_time" content={post.date} />
  <meta property="article:section" content="Shopping Tips" />
  <meta property="article:tag" content="Costco Price Adjustment" />
  
  <!-- Schema.org Markup -->
  <script type="application/ld+json">
    {schemaString}
  </script>
</svelte:head>

<article class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
  <header class="mb-8">
    <div class="flex items-center text-sm text-gray-500 mb-2">
      <time datetime={post.date}>{formatDate(post.date)}</time>
      <span class="mx-2">•</span>
      <span>{post.readingTime}</span>
    </div>
    <h1 class="text-4xl font-bold text-gray-900 mb-4">{post.title}</h1>
    <p class="text-xl text-gray-600">{post.description}</p>
  </header>
  
  <div class="prose prose-blue max-w-none">
    {@html renderedContent}
  </div>

  <!-- Contextual links for SEO and user engagement -->
  <div class="mt-12 pt-8 border-t border-gray-200">
    <div class="bg-gray-50 rounded-lg p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Ready to Put This Into Action?</h3>
      <p class="text-gray-600 mb-4">
        Start tracking prices and never miss a deal again with BargainHawk's free tools.
      </p>
      <div class="flex flex-wrap gap-3">
        <a href="/auth" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-dark transition-colors text-sm font-medium">
          Get Started Free
        </a>
        <a href="/feed" class="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors text-sm font-medium">
          View Price Drops
        </a>
        <a href="/community" class="text-primary hover:text-primary-dark underline text-sm font-medium">
          Join Community
        </a>
        <a href="/blog" class="text-primary hover:text-primary-dark underline text-sm font-medium">
          More Articles
        </a>
      </div>
    </div>

    <!-- Related links based on post content -->
    <div class="mt-6">
      <h4 class="text-md font-medium text-gray-900 mb-3">Related Resources:</h4>
      <div class="flex flex-wrap gap-4 text-sm">
        {#if post.slug.includes('costco')}
          <a href="/blog/costco-price-adjustment-hack" class="text-primary hover:text-primary-dark underline">
            Costco Price Adjustment Guide
          </a>
          <a href="/blog/how-to-access-costco-receipts" class="text-primary hover:text-primary-dark underline">
            Access Costco Receipts
          </a>
        {/if}
        {#if post.slug.includes('walmart')}
          <a href="/blog/walmart-price-adjustment-guide" class="text-primary hover:text-primary-dark underline">
            Walmart Price Adjustments
          </a>
        {/if}
        {#if post.slug.includes('deals') || post.slug.includes('savings')}
          <a href="/blog/best-deals-today" class="text-primary hover:text-primary-dark underline">
            Best Deals Today
          </a>
          <a href="/blog/money-saving-apps-canada" class="text-primary hover:text-primary-dark underline">
            Money Saving Apps
          </a>
        {/if}
        <a href="/faq" class="text-primary hover:text-primary-dark underline">
          FAQ
        </a>
        <a href="/about" class="text-primary hover:text-primary-dark underline">
          About BargainHawk
        </a>
      </div>
    </div>
  </div>
</article>