<script lang="ts">
  import { onMount } from 'svelte';
  import { user } from '../../stores/auth';
  import { supabase } from '../../utils/supabase';
  import { Monitor, Plus, Settings, Eye, Camera, X, Info } from 'lucide-svelte';
  
  interface ScreenshotData {
    id: string;
    url: string;
    screenshotUrl: string;
    coordinates?: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
    createdAt: string;
    updatedAt: string;
  }
  
  let screenshots: ScreenshotData[] = [];
  let loading = false;
  let error = '';
  let newUrl = '';
  let showAddForm = false;
  let selectedScreenshot: ScreenshotData | null = null;
  let showRectangleSelector = false;
  let showInfoTooltip = false;

  // Rectangle selector state
  let isDrawing = false;
  let startX = 0;
  let startY = 0;
  let currentX = 0;
  let currentY = 0;
  let imageElement: HTMLImageElement;
  let isDragging = false;
  let dragHandle = '';
  let selectionRect = { x: 0, y: 0, width: 0, height: 0 };
  let dragStartX = 0;
  let dragStartY = 0;
  let initialRect = { x: 0, y: 0, width: 0, height: 0 };
  let autoSaveTimeout: ReturnType<typeof setTimeout>;
  
  const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8080';
  
  onMount(() => {
    loadScreenshots();
    
    // Global mouse event listeners for dragging outside the image
    const handleGlobalMouseMove = (event: MouseEvent) => {
      if ((isDrawing || isDragging) && imageElement) {
        handleMouseMove(event);
      }
    };
    
    const handleGlobalMouseUp = () => {
      if (isDrawing || isDragging) {
        handleMouseUp();
      }
    };
    
    document.addEventListener('mousemove', handleGlobalMouseMove);
    document.addEventListener('mouseup', handleGlobalMouseUp);
    
    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove);
      document.removeEventListener('mouseup', handleGlobalMouseUp);
      if (autoSaveTimeout) {
        clearTimeout(autoSaveTimeout);
      }
    };
  });
  
  async function loadScreenshots() {
    if (!$user) return;

    // Get the current session to access the token
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.access_token) return;

    loading = true;
    try {
      const response = await fetch(`${API_BASE_URL}/screenshots`, {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        screenshots = await response.json();
      } else {
        error = 'Failed to load screenshots';
      }
    } catch (err) {
      error = 'Network error loading screenshots';
      console.error('Error loading screenshots:', err);
    } finally {
      loading = false;
    }
  }
  
  async function captureScreenshot() {
    if (!newUrl.trim()) {
      error = 'Please enter a valid URL';
      return;
    }

    if (!$user) {
      error = 'Authentication required';
      return;
    }

    // Get the current session to access the token
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.access_token) {
      error = 'Authentication required';
      return;
    }

    loading = true;
    error = '';

    try {
      const response = await fetch(`${API_BASE_URL}/screenshots`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ url: newUrl })
      });

      if (response.ok) {
        const newScreenshot = await response.json();
        screenshots = [newScreenshot, ...screenshots];
        newUrl = '';
        showAddForm = false;
        
        // Auto-open rectangle selector for new screenshots
        setTimeout(() => {
          openRectangleSelector(newScreenshot);
        }, 100);
      } else {
        const errorData = await response.json();
        error = errorData.message || 'Failed to capture screenshot';
      }
    } catch (err) {
      error = 'Network error capturing screenshot';
      console.error('Error capturing screenshot:', err);
    } finally {
      loading = false;
    }
  }
  
  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
  
  function openRectangleSelector(screenshot: ScreenshotData) {
    selectedScreenshot = screenshot;
    showRectangleSelector = true;

    // Initialize selection rectangle
    setTimeout(() => {
      if (imageElement) {
        const rect = imageElement.getBoundingClientRect();

        if (screenshot.coordinates) {
          // Use existing coordinates
          selectionRect = { ...screenshot.coordinates };
        } else {
          // Initialize to full screenshot
          selectionRect = {
            x: 0,
            y: 0,
            width: rect.width,
            height: rect.height
          };
        }

        // Set drawing coordinates to match selection
        startX = selectionRect.x;
        startY = selectionRect.y;
        currentX = selectionRect.x + selectionRect.width;
        currentY = selectionRect.y + selectionRect.height;
      }
    }, 100);
  }
  
  function closeRectangleSelector() {
    showRectangleSelector = false;
    selectedScreenshot = null;
    isDrawing = false;
    isDragging = false;
    dragHandle = '';
    selectionRect = { x: 0, y: 0, width: 0, height: 0 };
  }
  
  function handleMouseDown(event: MouseEvent) {
    if (!imageElement || isDragging) return;
    
    event.preventDefault();
    const rect = imageElement.getBoundingClientRect();
    startX = Math.max(0, Math.min(event.clientX - rect.left, rect.width));
    startY = Math.max(0, Math.min(event.clientY - rect.top, rect.height));
    currentX = startX;
    currentY = startY;
    isDrawing = true;
  }
  
  function handleHandleMouseDown(event: MouseEvent, handle: string) {
    event.preventDefault();
    event.stopPropagation();
    
    if (!imageElement) return;
    
    const rect = imageElement.getBoundingClientRect();
    dragStartX = Math.max(0, Math.min(event.clientX - rect.left, rect.width));
    dragStartY = Math.max(0, Math.min(event.clientY - rect.top, rect.height));
    initialRect = { ...selectionRect };
    dragHandle = handle;
    isDragging = true;
  }
  
  function handleBoxMouseDown(event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();

    if (!imageElement) return;

    const rect = imageElement.getBoundingClientRect();
    dragStartX = Math.max(0, Math.min(event.clientX - rect.left, rect.width));
    dragStartY = Math.max(0, Math.min(event.clientY - rect.top, rect.height));
    initialRect = { ...selectionRect };
    dragHandle = 'move';
    isDragging = true;
  }

  function handleMouseMove(event: MouseEvent) {
    if (!imageElement) return;

    const rect = imageElement.getBoundingClientRect();
    const mouseX = Math.max(0, Math.min(event.clientX - rect.left, rect.width));
    const mouseY = Math.max(0, Math.min(event.clientY - rect.top, rect.height));

    if (isDrawing) {
      currentX = mouseX;
      currentY = mouseY;

      // Update selection rectangle
      selectionRect = {
        x: Math.min(startX, currentX),
        y: Math.min(startY, currentY),
        width: Math.abs(currentX - startX),
        height: Math.abs(currentY - startY)
      };
    } else if (isDragging && dragHandle) {
      const deltaX = mouseX - dragStartX;
      const deltaY = mouseY - dragStartY;

      if (dragHandle === 'move') {
        // Move the entire rectangle
        selectionRect = {
          x: Math.max(0, Math.min(initialRect.x + deltaX, rect.width - initialRect.width)),
          y: Math.max(0, Math.min(initialRect.y + deltaY, rect.height - initialRect.height)),
          width: initialRect.width,
          height: initialRect.height
        };
      } else {
        // Handle resize operations
        let newRect = { ...initialRect };

        if (dragHandle.includes('n')) newRect.y = Math.max(0, initialRect.y + deltaY);
        if (dragHandle.includes('s')) newRect.height = Math.max(10, initialRect.height + deltaY);
        if (dragHandle.includes('w')) newRect.x = Math.max(0, initialRect.x + deltaX);
        if (dragHandle.includes('e')) newRect.width = Math.max(10, initialRect.width + deltaX);

        if (dragHandle.includes('n')) newRect.height = Math.max(10, initialRect.height - deltaY);
        if (dragHandle.includes('w')) newRect.width = Math.max(10, initialRect.width - deltaX);

        // Ensure rectangle stays within bounds
        newRect.x = Math.max(0, Math.min(newRect.x, rect.width - 10));
        newRect.y = Math.max(0, Math.min(newRect.y, rect.height - 10));
        newRect.width = Math.max(10, Math.min(newRect.width, rect.width - newRect.x));
        newRect.height = Math.max(10, Math.min(newRect.height, rect.height - newRect.y));

        selectionRect = newRect;
      }
    }
  }

  function scheduleAutoSave() {
    if (autoSaveTimeout) {
      clearTimeout(autoSaveTimeout);
    }

    // Schedule auto-save after 500ms of inactivity
    autoSaveTimeout = setTimeout(() => {
      if (selectionRect.width >= 10 && selectionRect.height >= 10) {
        saveSelection(true); // Silent auto-save
      }
    }, 500);
  }

  function handleMouseUp() {
    if (isDrawing) {
      isDrawing = false;
      scheduleAutoSave();
    }
    if (isDragging) {
      isDragging = false;
      dragHandle = '';
      scheduleAutoSave();
    }
  }

  async function saveSelection(silent = false) {
    if (!selectedScreenshot || !$user) return;

    // Get the current session to access the token
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.access_token) return;

    // Use current selection rectangle
    const { x, y, width, height } = selectionRect;

    // Ensure minimum selection size
    if (width < 10 || height < 10) {
      if (!silent) {
        error = 'Selection area is too small. Please select a larger area.';
      }
      return;
    }

    // Save coordinates
    try {
      const response = await fetch(`${API_BASE_URL}/screenshots/${selectedScreenshot.id}/coordinates`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ x, y, width, height })
      });

      if (response.ok) {
        await loadScreenshots();
        // Update the selected screenshot with new coordinates
        if (selectedScreenshot) {
          selectedScreenshot.coordinates = { x, y, width, height };
        }
        if (!silent) {
          error = ''; // Clear any previous errors only for manual saves
        }
      } else {
        if (!silent) {
          error = 'Failed to save coordinates';
        }
      }
    } catch (err) {
      if (!silent) {
        error = 'Network error saving coordinates';
      }
      console.error('Error saving coordinates:', err);
    }
  }
</script>

<div class="mb-8">
  <!-- Header with Info Icon -->
  <div class="flex items-center justify-between mb-6">
    <div class="flex items-center space-x-3">
      <Monitor class="h-6 w-6 text-primary" />
      <h2 class="text-2xl font-bold text-white">Website Tracking</h2>
      
      <!-- Info Icon with Tooltip -->
      <div class="relative">
        <button
          class="text-gray-400 hover:text-primary transition-colors"
          on:mouseenter={() => showInfoTooltip = true}
          on:mouseleave={() => showInfoTooltip = false}
          on:focus={() => showInfoTooltip = true}
          on:blur={() => showInfoTooltip = false}
        >
          <Info class="h-5 w-5" />
        </button>
        
        {#if showInfoTooltip}
          <div class="absolute left-0 top-8 z-10 w-80 p-3 bg-dark-lighter border border-gray-700 rounded-lg shadow-lg">
            <p class="text-sm text-gray-300">
              Track price changes on websites from stores other than Costco, Walmart, and Wayfair. 
              Perfect for monitoring deals from specialty retailers, local stores, or any website with pricing information.
            </p>
          </div>
        {/if}
      </div>
    </div>
    
    <button
      class="flex items-center space-x-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
      on:click={() => showAddForm = !showAddForm}
      disabled={loading}
    >
      <Plus class="h-5 w-5" />
      <span>Add Website</span>
    </button>
  </div>

  <!-- Add Website Form -->
  {#if showAddForm}
    <div class="bg-dark-lighter/50 rounded-lg p-6 mb-6 border border-gray-800/50 backdrop-blur-sm">
      <h3 class="text-lg font-semibold mb-4 text-white">Capture Website Screenshot</h3>
      <div class="flex space-x-4">
        <input
          type="url"
          bind:value={newUrl}
          placeholder="Enter website URL (e.g., https://example.com)"
          class="flex-1 px-4 py-2 bg-dark border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary"
          disabled={loading}
        />
        <button
          class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors disabled:opacity-50"
          on:click={captureScreenshot}
          disabled={loading || !newUrl.trim()}
        >
          {#if loading}
            <div class="flex items-center space-x-2">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Capturing...</span>
            </div>
          {:else}
            <div class="flex items-center space-x-2">
              <Camera class="h-4 w-4" />
              <span>Capture</span>
            </div>
          {/if}
        </button>
      </div>
    </div>
  {/if}

  <!-- Error Message -->
  {#if error}
    <div class="bg-red-900/50 border border-red-700 text-red-200 px-4 py-3 rounded-lg mb-6">
      {error}
    </div>
  {/if}

  <!-- Screenshots Grid -->
  {#if loading && screenshots.length === 0}
    <div class="flex items-center justify-center py-8">
      <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
      <span class="ml-3 text-gray-400">Loading screenshots...</span>
    </div>
  {:else if screenshots.length === 0}
    <div class="text-center py-8 bg-dark-lighter/30 rounded-lg border border-gray-800/30 backdrop-blur-sm">
      <Monitor class="h-12 w-12 text-gray-600 mx-auto mb-3" />
      <h3 class="text-lg font-semibold text-gray-400 mb-2">No websites tracked yet</h3>
      <p class="text-gray-500 mb-4">Track price changes on any website</p>
      <button
        class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
        on:click={() => showAddForm = true}
      >
        Add Your First Website
      </button>
    </div>
  {:else}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {#each screenshots.slice(0, 6) as screenshot}
        <div class="bg-dark-lighter/50 rounded-lg border border-gray-800/50 overflow-hidden backdrop-blur-sm">
          <!-- Screenshot Image -->
          <div class="aspect-video bg-gray-900 relative overflow-hidden">
            <img
              src={screenshot.screenshotUrl}
              alt="Screenshot of {screenshot.url}"
              class="w-full h-full object-cover"
              loading="lazy"
            />
          </div>

          <!-- Card Content -->
          <div class="p-3">
            <h4 class="font-medium text-white mb-1 truncate text-sm" title={screenshot.url}>
              {screenshot.url}
            </h4>
            <p class="text-xs text-gray-400 mb-3">
              Captured {formatDate(screenshot.createdAt)}
            </p>

            <!-- Actions -->
            <div class="flex space-x-2">
              <button
                class="flex-1 flex items-center justify-center space-x-1 px-2 py-1.5 bg-primary/20 text-primary rounded text-xs hover:bg-primary/30 transition-colors"
                on:click={() => openRectangleSelector(screenshot)}
              >
                <Settings class="h-3 w-3" />
                <span>{screenshot.coordinates ? 'Edit Area' : 'Select Area'}</span>
              </button>
              <button
                class="px-2 py-1.5 bg-gray-700 text-gray-300 rounded hover:bg-gray-600 transition-colors"
                on:click={() => window.open(screenshot.url, '_blank')}
              >
                <Eye class="h-3 w-3" />
              </button>
            </div>
          </div>
        </div>
      {/each}
    </div>

    {#if screenshots.length > 6}
      <div class="text-center mt-4">
        <button
          class="text-primary hover:text-primary-dark transition-colors text-sm"
          on:click={() => window.location.href = '/website-tracking'}
        >
          View all {screenshots.length} tracked websites →
        </button>
      </div>
    {/if}
  {/if}
</div>

<!-- Rectangle Selector Modal -->
{#if showRectangleSelector && selectedScreenshot}
  <div class="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
    <div class="bg-dark-lighter rounded-lg border border-gray-800 max-w-6xl w-full max-h-[90vh] overflow-auto">
      <!-- Modal Header -->
      <div class="flex items-center justify-between p-4 border-b border-gray-800">
        <div>
          <h3 class="text-lg font-semibold text-white">Select Area to Monitor</h3>
          <p class="text-sm text-gray-400 truncate">{selectedScreenshot.url}</p>
        </div>
        <button
          class="text-gray-400 hover:text-white transition-colors"
          on:click={closeRectangleSelector}
        >
          <X class="h-6 w-6" />
        </button>
      </div>

      <!-- Modal Content -->
      <div class="p-4">
        <div class="relative inline-block bg-gray-900 rounded-lg overflow-hidden">
          <!-- Image Container with Overlay -->
          <div class="relative">
            <img
              bind:this={imageElement}
              src={selectedScreenshot.screenshotUrl}
              alt="Screenshot"
              class="max-w-full h-auto block"
            />

            <!-- Overlay Container -->
            <div
              class="absolute inset-0 cursor-crosshair"
              on:mousedown={handleMouseDown}
              role="button"
              tabindex="0"
              aria-label="Select area to monitor"
            >
              <!-- Shaded overlay for unselected areas -->
              {#if selectionRect.width > 0 && selectionRect.height > 0 && imageElement}
                <!-- Top overlay -->
                <div
                  class="absolute bg-black/60 pointer-events-none"
                  style="left: 0; top: 0; width: 100%; height: {selectionRect.y}px;"
                ></div>

                <!-- Bottom overlay -->
                <div
                  class="absolute bg-black/60 pointer-events-none"
                  style="left: 0; top: {selectionRect.y + selectionRect.height}px; width: 100%; height: calc(100% - {selectionRect.y + selectionRect.height}px);"
                ></div>

                <!-- Left overlay -->
                <div
                  class="absolute bg-black/60 pointer-events-none"
                  style="left: 0; top: {selectionRect.y}px; width: {selectionRect.x}px; height: {selectionRect.height}px;"
                ></div>

                <!-- Right overlay -->
                <div
                  class="absolute bg-black/60 pointer-events-none"
                  style="left: {selectionRect.x + selectionRect.width}px; top: {selectionRect.y}px; width: calc(100% - {selectionRect.x + selectionRect.width}px); height: {selectionRect.height}px;"
                ></div>
              {/if}

            <!-- Selection Rectangle -->
            {#if selectionRect.width > 0 && selectionRect.height > 0}
              <div
                class="absolute border-4 border-blue-500 bg-blue-500/10 shadow-lg cursor-move"
                style="left: {selectionRect.x}px; top: {selectionRect.y}px; width: {selectionRect.width}px; height: {selectionRect.height}px;"
                on:mousedown={handleBoxMouseDown}
                role="button"
                tabindex="0"
                aria-label="Drag to move selection"
              >
                <!-- Corner handles -->
                <div
                  class="absolute -top-2 -left-2 w-4 h-4 bg-blue-500 border-2 border-white rounded-full cursor-nw-resize hover:bg-blue-600 transition-colors"
                  on:mousedown={(e) => handleHandleMouseDown(e, 'nw')}
                  role="button"
                  tabindex="0"
                  aria-label="Resize from top-left corner"
                ></div>
                <div
                  class="absolute -top-2 -right-2 w-4 h-4 bg-blue-500 border-2 border-white rounded-full cursor-ne-resize hover:bg-blue-600 transition-colors"
                  on:mousedown={(e) => handleHandleMouseDown(e, 'ne')}
                  role="button"
                  tabindex="0"
                  aria-label="Resize from top-right corner"
                ></div>
                <div
                  class="absolute -bottom-2 -left-2 w-4 h-4 bg-blue-500 border-2 border-white rounded-full cursor-sw-resize hover:bg-blue-600 transition-colors"
                  on:mousedown={(e) => handleHandleMouseDown(e, 'sw')}
                  role="button"
                  tabindex="0"
                  aria-label="Resize from bottom-left corner"
                ></div>
                <div
                  class="absolute -bottom-2 -right-2 w-4 h-4 bg-blue-500 border-2 border-white rounded-full cursor-se-resize hover:bg-blue-600 transition-colors"
                  on:mousedown={(e) => handleHandleMouseDown(e, 'se')}
                  role="button"
                  tabindex="0"
                  aria-label="Resize from bottom-right corner"
                ></div>

                <!-- Edge handles -->
                <div
                  class="absolute -top-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-blue-500 border-2 border-white rounded-full cursor-n-resize hover:bg-blue-600 transition-colors"
                  on:mousedown={(e) => handleHandleMouseDown(e, 'n')}
                  role="button"
                  tabindex="0"
                  aria-label="Resize from top edge"
                ></div>
                <div
                  class="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-blue-500 border-2 border-white rounded-full cursor-s-resize hover:bg-blue-600 transition-colors"
                  on:mousedown={(e) => handleHandleMouseDown(e, 's')}
                  role="button"
                  tabindex="0"
                  aria-label="Resize from bottom edge"
                ></div>
                <div
                  class="absolute -left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 bg-blue-500 border-2 border-white rounded-full cursor-w-resize hover:bg-blue-600 transition-colors"
                  on:mousedown={(e) => handleHandleMouseDown(e, 'w')}
                  role="button"
                  tabindex="0"
                  aria-label="Resize from left edge"
                ></div>
                <div
                  class="absolute -right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 bg-blue-500 border-2 border-white rounded-full cursor-e-resize hover:bg-blue-600 transition-colors"
                  on:mousedown={(e) => handleHandleMouseDown(e, 'e')}
                  role="button"
                  tabindex="0"
                  aria-label="Resize from right edge"
                ></div>
              </div>
            {/if}
            </div>
          </div>
        </div>

        <!-- Instructions and Status -->
        <div class="mt-4 text-sm text-gray-400">
          {#if selectionRect.width > 0 && selectionRect.height > 0}
            Selection: {Math.round(selectionRect.width)} × {Math.round(selectionRect.height)} pixels
            {#if selectionRect.width >= 10 && selectionRect.height >= 10}
              <span class="text-green-400 ml-2">• Auto-saving changes</span>
            {:else}
              <span class="text-yellow-400 ml-2">• Selection too small (minimum 10×10)</span>
            {/if}
          {:else}
            Click and drag to select an area to monitor
          {/if}
        </div>
      </div>
    </div>
  </div>
{/if}
