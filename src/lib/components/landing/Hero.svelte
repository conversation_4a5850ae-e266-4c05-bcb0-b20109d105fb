  <script lang="ts">
    import { fade, fly } from 'svelte/transition';
    import { DollarSign, Bell, Clock } from 'lucide-svelte';
    import Button from '../ui/Button.svelte';
    import AuthModal from '../auth/AuthModal.svelte';
    import { user } from '../../stores/auth';
    import { page } from '../../stores/navigation';
    
    let showAuthModal = false;
    
    const features = [
      { icon: DollarSign, text: 'Save money with automatic price tracking' },
      { icon: Bell, text: 'Get instant price drop notifications' },
      { icon: Clock, text: 'Never miss a price adjustment window' }
    ];

    function handleGetStarted() {
      if ($user) {
        page.set('dashboard');
      } else {
        page.set('auth');
      }
    }

    $: buttonText = $user ? 'View Your Dashboard' : 'Get Started - It\'s Free';
  </script>

  <section class="relative overflow-hidden gradient-bg py-16 md:py-32 px-4">
    <div class="relative max-w-7xl mx-auto">
      <div class="text-center" in:fade={{ duration: 1000, delay: 200 }}>
        <h1 class="text-4xl md:text-6xl font-extrabold tracking-tight">
          <span class="block text-gray-100">Never Miss a</span>
          <span class="block text-primary mt-2">Price Drop</span>
        </h1>
        
        <p class="mt-6 md:mt-8 max-w-2xl mx-auto text-lg md:text-xl text-gray-300 px-4">
          Track prices automatically on Costco and Wayfair. Get notified when your items go on sale.
        </p>
        
        <!-- Feature List -->
        <div class="mt-8 md:mt-12 flex flex-col md:flex-row justify-center gap-4 md:gap-6 px-4">
          {#each features as feature, i}
            <div 
              class="flex items-center gap-3 bg-dark-lighter/50 backdrop-blur-sm px-4 py-3 rounded-lg border border-gray-700"
              in:fly={{ y: 20, duration: 400, delay: 400 + i * 100 }}
            >
              <svelte:component this={feature.icon} class="w-5 h-5 text-primary flex-shrink-0" />
              <span class="text-sm md:text-base text-gray-300">{feature.text}</span>
            </div>
          {/each}
        </div>
        
        <!-- CTA Button -->
        <div class="mt-8 md:mt-12 px-4" in:fly={{ y: 20, duration: 400, delay: 700 }}>
          <Button
            customClass="text-base md:text-lg px-6 md:px-8 py-3 md:py-4 shadow-glow hover:shadow-lg transform hover:-translate-y-0.5 transition-all font-semibold w-full md:w-auto"
            on:click={handleGetStarted}
          >
            {buttonText}
          </Button>

          <!-- Additional contextual links for SEO -->
          <div class="mt-6 text-center">
            <p class="text-gray-400 text-sm">
              Already tracking prices? <a href="/feed" class="text-primary hover:text-primary-light underline">View latest price drops</a> •
              <a href="/blog/costco-price-adjustment-hack" class="text-primary hover:text-primary-light underline">Learn about Costco price adjustments</a> •
              <a href="/community" class="text-primary hover:text-primary-light underline">Join our community</a>
            </p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <AuthModal
    show={showAuthModal}
    on:close={() => showAuthModal = false}
  />