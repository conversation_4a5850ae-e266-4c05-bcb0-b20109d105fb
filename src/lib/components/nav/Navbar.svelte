<script lang="ts">
    import { page } from '../../stores/navigation';
    import { user } from '../../stores/auth';
    import Logo from './Logo.svelte';
    import NavLink from './NavLink.svelte';
    import UserMenu from '../UserMenu.svelte';
    import { Menu } from 'lucide-svelte';
    
    let isMenuOpen = false;
    
    const links = [
      { href: 'feed', label: 'Price Drops' },
      { href: 'price-history', label: 'Price History', requiresAuth: true },
      { href: 'community', label: 'Community', hasBeta: true },
      { href: 'blog', label: 'Blog' },
      { href: 'about', label: 'About' }
    ];
  
    function handleNavigation(href: string) {
      if (href === 'price-history' && !$user) {
        // Handle auth required
        return;
      }
      page.set(href);
      isMenuOpen = false;
    }
  
    $: filteredLinks = links.filter(link => !link.requiresAuth || $user);
  </script>
  
  <nav class="fixed top-0 left-0 right-0 z-50 border-b border-gray-800/50 backdrop-blur-xl bg-dark-navbar/80">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        <!-- Logo -->
        <Logo />
  
        <!-- Desktop Navigation -->
        <div class="hidden md:flex items-center space-x-1">
          {#each filteredLinks as link}
            <NavLink
              href={link.href}
              active={$page === link.href}
              on:click={() => handleNavigation(link.href)}
            >
              <span class="flex items-center gap-2">
                {link.label}
                {#if link.hasBeta}
                  <span class="bg-blue-500 text-white text-xs px-2 py-0.5 rounded-full font-medium">BETA</span>
                {/if}
              </span>
            </NavLink>
          {/each}
          
          {#if $user}
            <UserMenu />
          {:else}
            <button
              class="ml-4 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark 
                     transition-all duration-200 text-sm font-medium"
              on:click={() => handleNavigation('login')}
            >
              Sign In
            </button>
          {/if}
        </div>
  
        <!-- Mobile menu button -->
        <button
          class="md:hidden p-2 rounded-lg text-gray-400 hover:text-white hover:bg-dark-lighter"
          on:click={() => isMenuOpen = !isMenuOpen}
          aria-label="Menu"
        >
          <Menu size={24} />
        </button>
      </div>
  
      <!-- Mobile Navigation -->
      {#if isMenuOpen}
        <div class="md:hidden py-2 space-y-1">
          {#each filteredLinks as link}
            <button
              class="w-full text-left px-3 py-2 text-gray-300 hover:text-white hover:bg-dark-lighter
                     rounded-lg transition-colors flex items-center justify-between {$page === link.href ? 'bg-dark-lighter text-white' : ''}"
              on:click={() => handleNavigation(link.href)}
            >
              {link.label}
              {#if link.hasBeta}
                <span class="bg-blue-500 text-white text-xs px-2 py-0.5 rounded-full font-medium">BETA</span>
              {/if}
            </button>
          {/each}
          
          {#if !$user}
            <button
              class="w-full mt-4 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark 
                     transition-all duration-200 text-sm font-medium"
              on:click={() => handleNavigation('login')}
            >
              Sign In
            </button>
          {/if}
        </div>
      {/if}
    </div>
  </nav>
  
  <!-- Spacer to prevent content from going under fixed navbar -->
  <div class="h-16"></div>