<script lang="ts">
  import { onMount, createEventDispatcher } from 'svelte';
  import { user } from '../../stores/auth';
  import { supabase } from '../../utils/supabase';
  import { X } from 'lucide-svelte';
  
  export let screenshot: {
    id: string;
    url: string;
    screenshotUrl: string;
    coordinates?: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
  };
  export let show = false;
  
  const dispatch = createEventDispatcher();
  
  // Rectangle selector state
  let isDrawing = false;
  let startX = 0;
  let startY = 0;
  let currentX = 0;
  let currentY = 0;
  let imageElement: HTMLImageElement;
  let isDragging = false;
  let dragHandle = '';
  let selectionRect = { x: 0, y: 0, width: 0, height: 0 };
  let dragStartX = 0;
  let dragStartY = 0;
  let initialRect = { x: 0, y: 0, width: 0, height: 0 };
  let autoSaveTimeout: ReturnType<typeof setTimeout>;
  let error = '';
  
  const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8080';
  
  onMount(() => {
    // Global mouse event listeners for dragging outside the image
    const handleGlobalMouseMove = (event: MouseEvent) => {
      if ((isDrawing || isDragging) && imageElement) {
        handleMouseMove(event);
      }
    };
    
    const handleGlobalMouseUp = () => {
      if (isDrawing || isDragging) {
        handleMouseUp();
      }
    };
    
    document.addEventListener('mousemove', handleGlobalMouseMove);
    document.addEventListener('mouseup', handleGlobalMouseUp);
    
    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove);
      document.removeEventListener('mouseup', handleGlobalMouseUp);
      if (autoSaveTimeout) {
        clearTimeout(autoSaveTimeout);
      }
    };
  });
  
  // Initialize selection when screenshot changes or modal opens
  $: if (show && screenshot && imageElement) {
    setTimeout(() => {
      const rect = imageElement.getBoundingClientRect();
      
      if (screenshot.coordinates) {
        // Use existing coordinates
        selectionRect = { ...screenshot.coordinates };
      } else {
        // Initialize to full screenshot
        selectionRect = {
          x: 0,
          y: 0,
          width: rect.width,
          height: rect.height
        };
      }
      
      // Set drawing coordinates to match selection
      startX = selectionRect.x;
      startY = selectionRect.y;
      currentX = selectionRect.x + selectionRect.width;
      currentY = selectionRect.y + selectionRect.height;
    }, 100);
  }
  
  function close() {
    show = false;
    isDrawing = false;
    isDragging = false;
    dragHandle = '';
    selectionRect = { x: 0, y: 0, width: 0, height: 0 };
    error = '';
    dispatch('close');
  }
  
  function handleMouseDown(event: MouseEvent) {
    if (!imageElement || isDragging) return;
    
    event.preventDefault();
    const rect = imageElement.getBoundingClientRect();
    startX = Math.max(0, Math.min(event.clientX - rect.left, rect.width));
    startY = Math.max(0, Math.min(event.clientY - rect.top, rect.height));
    currentX = startX;
    currentY = startY;
    isDrawing = true;
  }
  
  function handleHandleMouseDown(event: MouseEvent, handle: string) {
    event.preventDefault();
    event.stopPropagation();
    
    if (!imageElement) return;
    
    const rect = imageElement.getBoundingClientRect();
    dragStartX = Math.max(0, Math.min(event.clientX - rect.left, rect.width));
    dragStartY = Math.max(0, Math.min(event.clientY - rect.top, rect.height));
    initialRect = { ...selectionRect };
    dragHandle = handle;
    isDragging = true;
  }
  
  function handleBoxMouseDown(event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();
    
    if (!imageElement) return;
    
    const rect = imageElement.getBoundingClientRect();
    dragStartX = Math.max(0, Math.min(event.clientX - rect.left, rect.width));
    dragStartY = Math.max(0, Math.min(event.clientY - rect.top, rect.height));
    initialRect = { ...selectionRect };
    dragHandle = 'move';
    isDragging = true;
  }
  
  function handleMouseMove(event: MouseEvent) {
    if (!imageElement) return;
    
    const rect = imageElement.getBoundingClientRect();
    const mouseX = Math.max(0, Math.min(event.clientX - rect.left, rect.width));
    const mouseY = Math.max(0, Math.min(event.clientY - rect.top, rect.height));
    
    if (isDrawing) {
      currentX = mouseX;
      currentY = mouseY;
      
      // Update selection rectangle
      selectionRect = {
        x: Math.min(startX, currentX),
        y: Math.min(startY, currentY),
        width: Math.abs(currentX - startX),
        height: Math.abs(currentY - startY)
      };
    } else if (isDragging && dragHandle) {
      const deltaX = mouseX - dragStartX;
      const deltaY = mouseY - dragStartY;
      
      if (dragHandle === 'move') {
        // Move the entire rectangle
        selectionRect = {
          x: Math.max(0, Math.min(initialRect.x + deltaX, rect.width - initialRect.width)),
          y: Math.max(0, Math.min(initialRect.y + deltaY, rect.height - initialRect.height)),
          width: initialRect.width,
          height: initialRect.height
        };
      } else {
        // Handle resize operations
        let newRect = { ...initialRect };
        
        if (dragHandle.includes('n')) newRect.y = Math.max(0, initialRect.y + deltaY);
        if (dragHandle.includes('s')) newRect.height = Math.max(10, initialRect.height + deltaY);
        if (dragHandle.includes('w')) newRect.x = Math.max(0, initialRect.x + deltaX);
        if (dragHandle.includes('e')) newRect.width = Math.max(10, initialRect.width + deltaX);
        
        if (dragHandle.includes('n')) newRect.height = Math.max(10, initialRect.height - deltaY);
        if (dragHandle.includes('w')) newRect.width = Math.max(10, initialRect.width - deltaX);
        
        // Ensure rectangle stays within bounds
        newRect.x = Math.max(0, Math.min(newRect.x, rect.width - 10));
        newRect.y = Math.max(0, Math.min(newRect.y, rect.height - 10));
        newRect.width = Math.max(10, Math.min(newRect.width, rect.width - newRect.x));
        newRect.height = Math.max(10, Math.min(newRect.height, rect.height - newRect.y));
        
        selectionRect = newRect;
      }
    }
  }
  
  function scheduleAutoSave() {
    if (autoSaveTimeout) {
      clearTimeout(autoSaveTimeout);
    }
    
    // Schedule auto-save after 500ms of inactivity
    autoSaveTimeout = setTimeout(() => {
      if (selectionRect.width >= 10 && selectionRect.height >= 10) {
        saveSelection(true); // Silent auto-save
      }
    }, 500);
  }
  
  function handleMouseUp() {
    if (isDrawing) {
      isDrawing = false;
      scheduleAutoSave();
    }
    if (isDragging) {
      isDragging = false;
      dragHandle = '';
      scheduleAutoSave();
    }
  }
  
  async function saveSelection(silent = false) {
    if (!screenshot || !$user) return;

    // Get the current session to access the token
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.access_token) return;

    // Use current selection rectangle
    const { x, y, width, height } = selectionRect;

    // Ensure minimum selection size
    if (width < 10 || height < 10) {
      if (!silent) {
        error = 'Selection area is too small. Please select a larger area.';
      }
      return;
    }

    // Save coordinates
    try {
      const response = await fetch(`${API_BASE_URL}/screenshots/${screenshot.id}/coordinates`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ x, y, width, height })
      });

      if (response.ok) {
        // Update the screenshot with new coordinates
        screenshot.coordinates = { x, y, width, height };
        dispatch('saved', { x, y, width, height });
        if (!silent) {
          error = ''; // Clear any previous errors only for manual saves
        }
      } else {
        if (!silent) {
          error = 'Failed to save coordinates';
        }
      }
    } catch (err) {
      if (!silent) {
        error = 'Network error saving coordinates';
      }
      console.error('Error saving coordinates:', err);
    }
  }
</script>

{#if show}
  <div class="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
    <div class="bg-dark-lighter rounded-lg border border-gray-800 max-w-6xl w-full max-h-[90vh] overflow-auto">
      <!-- Modal Header -->
      <div class="flex items-center justify-between p-4 border-b border-gray-800">
        <div>
          <h3 class="text-lg font-semibold text-white">Select Area to Monitor</h3>
        </div>
        <button
          class="text-gray-400 hover:text-white transition-colors"
          on:click={close}
        >
          <X class="h-6 w-6" />
        </button>
      </div>
      
      <!-- Modal Content -->
      <div class="p-4">
        <div class="relative inline-block bg-gray-900 rounded-lg overflow-hidden">
          <!-- Image Container with Overlay -->
          <div class="relative">
            <img
              bind:this={imageElement}
              src={screenshot.screenshotUrl}
              alt="Screenshot"
              class="max-w-full h-auto block"
            />
            
            <!-- Selection Overlay -->
            <div
              class="absolute inset-0 bg-black/30 pointer-events-none"
              style="clip-path: polygon(0% 0%, 0% 100%, {selectionRect.x}px 100%, {selectionRect.x}px {selectionRect.y}px, {selectionRect.x + selectionRect.width}px {selectionRect.y}px, {selectionRect.x + selectionRect.width}px {selectionRect.y + selectionRect.height}px, {selectionRect.x}px {selectionRect.y + selectionRect.height}px, {selectionRect.x}px 100%, 100% 100%, 100% 0%)"
            ></div>
            
            <!-- Selection Rectangle -->
            {#if selectionRect.width > 0 && selectionRect.height > 0}
              <div
                class="absolute border-2 border-primary bg-primary/10 cursor-move"
                style="left: {selectionRect.x}px; top: {selectionRect.y}px; width: {selectionRect.width}px; height: {selectionRect.height}px;"
                on:mousedown={handleBoxMouseDown}
                role="button"
                tabindex="0"
              >
                <!-- Resize Handles -->
                <div class="absolute -top-1 -left-1 w-3 h-3 bg-primary border border-white cursor-nw-resize" on:mousedown={(e) => handleHandleMouseDown(e, 'nw')} role="button" tabindex="0"></div>
                <div class="absolute -top-1 left-1/2 -translate-x-1/2 w-3 h-3 bg-primary border border-white cursor-n-resize" on:mousedown={(e) => handleHandleMouseDown(e, 'n')} role="button" tabindex="0"></div>
                <div class="absolute -top-1 -right-1 w-3 h-3 bg-primary border border-white cursor-ne-resize" on:mousedown={(e) => handleHandleMouseDown(e, 'ne')} role="button" tabindex="0"></div>
                <div class="absolute top-1/2 -translate-y-1/2 -right-1 w-3 h-3 bg-primary border border-white cursor-e-resize" on:mousedown={(e) => handleHandleMouseDown(e, 'e')} role="button" tabindex="0"></div>
                <div class="absolute -bottom-1 -right-1 w-3 h-3 bg-primary border border-white cursor-se-resize" on:mousedown={(e) => handleHandleMouseDown(e, 'se')} role="button" tabindex="0"></div>
                <div class="absolute -bottom-1 left-1/2 -translate-x-1/2 w-3 h-3 bg-primary border border-white cursor-s-resize" on:mousedown={(e) => handleHandleMouseDown(e, 's')} role="button" tabindex="0"></div>
                <div class="absolute -bottom-1 -left-1 w-3 h-3 bg-primary border border-white cursor-sw-resize" on:mousedown={(e) => handleHandleMouseDown(e, 'sw')} role="button" tabindex="0"></div>
                <div class="absolute top-1/2 -translate-y-1/2 -left-1 w-3 h-3 bg-primary border border-white cursor-w-resize" on:mousedown={(e) => handleHandleMouseDown(e, 'w')} role="button" tabindex="0"></div>
              </div>
            {/if}
            
            <!-- Drawing Layer -->
            <div
              class="absolute inset-0 cursor-crosshair"
              on:mousedown={handleMouseDown}
              role="button"
              tabindex="0"
            ></div>
          </div>
        </div>
        
        <!-- Instructions and Status -->
        <div class="mt-4 text-sm text-gray-400">
          {#if selectionRect.width > 0 && selectionRect.height > 0}
            Selection: {Math.round(selectionRect.width)} × {Math.round(selectionRect.height)} pixels
            {#if selectionRect.width >= 10 && selectionRect.height >= 10}
              <span class="text-green-400 ml-2">• Auto-saving changes</span>
            {:else}
              <span class="text-yellow-400 ml-2">• Selection too small (minimum 10×10)</span>
            {/if}
          {:else}
            Click and drag to select an area to monitor
          {/if}
        </div>
        
        <!-- Error Message -->
        {#if error}
          <div class="mt-2 text-sm text-red-400">
            {error}
          </div>
        {/if}
      </div>
    </div>
  </div>
{/if}
