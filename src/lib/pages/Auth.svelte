<script lang="ts">
  import { onMount } from 'svelte';
  import { fade, fly } from 'svelte/transition';
  import { 
    Mail, 
    Lock, 
    Eye, 
    EyeOff, 
    Shield, 
    Users, 
    TrendingUp,
    CheckCircle,
    ArrowRight
  } from 'lucide-svelte';
  import Button from '../components/ui/Button.svelte';
  import { signIn, signUp, user } from '../stores/auth';
  import { page } from '../stores/navigation';
  import ForgotPasswordForm from '../components/auth/ForgotPasswordForm.svelte';
  import { supabase } from '../utils/supabase';

  let isLogin = false; // Default to Create Account
  let showPassword = false;
  let email = '';
  let password = '';
  let confirmPassword = '';
  let loading = false;
  let mounted = false;
  let error = '';
  let showConfirmation = false;
  let showForgotPassword = false;

  onMount(() => {
    mounted = true;
    // Redirect if already authenticated
    if ($user) {
      page.set('dashboard');
    }
  });

  function toggleAuthMode() {
    isLogin = !isLogin;
    // Reset form and states
    email = '';
    password = '';
    confirmPassword = '';
    error = '';
    showForgotPassword = false;
    showConfirmation = false;
  }

  function togglePasswordVisibility() {
    showPassword = !showPassword;
  }

  async function handleSubmit() {
    if (!email || !password) return;

    // Validate passwords match for sign up
    if (!isLogin && password !== confirmPassword) {
      error = 'Passwords do not match';
      return;
    }

    try {
      loading = true;
      error = '';

      if (isLogin) {
        await signIn(email, password);
        // Redirect to dashboard after successful sign in
        page.set('dashboard');
      } else {
        await signUp(email, password);
        // Show confirmation message for sign up
        showConfirmation = true;
      }
    } catch (e) {
      error = e instanceof Error ? e.message : 'An error occurred';
    } finally {
      loading = false;
    }
  }

  function showForgotPasswordForm() {
    showForgotPassword = true;
    error = '';
  }

  function backToSignIn() {
    showForgotPassword = false;
    error = '';
  }

  function closeForgotPassword() {
    showForgotPassword = false;
    error = '';
  }

  async function handleGoogleAuth() {
    try {
      loading = true;
      error = '';

      const { error: googleError } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/dashboard`
        }
      });

      if (googleError) throw googleError;
    } catch (e) {
      error = e instanceof Error ? e.message : 'Failed to sign in with Google';
      loading = false;
    }
  }

  const trustIndicators = [
    { icon: Shield, text: 'Secure & encrypted' },
    { icon: Users, text: 'Trusted by thousands' },
    { icon: TrendingUp, text: 'Save money automatically' }
  ];

  const benefits = [
    'Track prices automatically',
    'Get instant price drop alerts',
    'Never miss a price adjustment',
    'Save money effortlessly'
  ];
</script>

<svelte:head>
  <title>{isLogin ? 'Sign In' : 'Create Account'} | BargainHawk</title>
  <meta name="description" content="{isLogin ? 'Sign in to' : 'Join'} BargainHawk and start saving money with automatic price tracking and alerts." />
</svelte:head>

{#if mounted}
<div class="min-h-screen gradient-bg flex">
  <!-- Left Side - Branding & Benefits -->
  <div class="hidden lg:flex lg:w-1/2 relative overflow-hidden">
    <div class="absolute inset-0 bg-gradient-to-br from-primary/20 to-primary/5"></div>
    
    <div class="relative z-10 flex flex-col justify-center px-12 py-16">
      <!-- Logo -->
      <div class="mb-12" in:fade={{ duration: 800, delay: 200 }}>
        <div class="flex items-center mb-6">
          <img src="/BargainHawk.svg" alt="BargainHawk" class="h-12 w-12 mr-4" />
          <span class="text-3xl font-bold text-white">BargainHawk</span>
        </div>
        <p class="text-xl text-gray-300 leading-relaxed">
          Never miss a price drop again. Track Costco prices automatically and get notified when you can claim price adjustments.
        </p>
      </div>

      <!-- Trust Indicators -->
      <div class="mb-12" in:fly={{ y: 30, duration: 600, delay: 400 }}>
        <h3 class="text-lg font-semibold text-white mb-6">Why users trust BargainHawk</h3>
        <div class="space-y-4">
          {#each trustIndicators as indicator, i}
            <div 
              class="flex items-center space-x-3"
              in:fly={{ x: -20, duration: 400, delay: 600 + (i * 100) }}
            >
              <div class="p-2 bg-primary/10 rounded-lg">
                <svelte:component this={indicator.icon} class="h-5 w-5 text-primary" />
              </div>
              <span class="text-gray-300">{indicator.text}</span>
            </div>
          {/each}
        </div>
      </div>

      <!-- Benefits -->
      <div in:fly={{ y: 30, duration: 600, delay: 800 }}>
        <h3 class="text-lg font-semibold text-white mb-6">What you'll get</h3>
        <div class="space-y-3">
          {#each benefits as benefit, i}
            <div 
              class="flex items-center space-x-3"
              in:fly={{ x: -20, duration: 400, delay: 1000 + (i * 100) }}
            >
              <CheckCircle class="h-5 w-5 text-green-400 flex-shrink-0" />
              <span class="text-gray-300">{benefit}</span>
            </div>
          {/each}
        </div>
      </div>
    </div>
  </div>

  <!-- Right Side - Auth Form -->
  <div class="w-full lg:w-1/2 flex items-center justify-center px-6 py-12">
    <div class="w-full max-w-md">


      <!-- Auth Form Card -->
      <div 
        class="bg-dark-lighter/50 backdrop-blur-xl rounded-2xl border border-gray-800/50 p-8 shadow-2xl"
        in:fly={{ y: 30, duration: 600, delay: 200 }}
      >
        {#if showConfirmation}
          <!-- Confirmation Message -->
          <div class="text-center mb-8">
            <h1 class="text-2xl font-bold text-white mb-2">Check your email</h1>
            <p class="text-gray-400">
              We've sent a confirmation link to <strong class="text-white">{email}</strong>
            </p>
            <p class="text-sm text-gray-500 mt-2">
              Click the link in the email to complete your account setup.
            </p>
            <div class="mt-6">
              <Button
                customClass="w-full py-3"
                on:click={() => page.set('home')}
              >
                Back to Home
              </Button>
            </div>
          </div>
        {:else if showForgotPassword}
          <!-- Forgot Password Form -->
          <div class="mb-8">
            <h1 class="text-2xl font-bold text-white mb-6 text-center">Reset Password</h1>
            <ForgotPasswordForm
              on:close={closeForgotPassword}
              on:back={backToSignIn}
            />
          </div>
        {:else}

        {#if !showConfirmation}
        <!-- Google Auth Button -->
        <button
          type="button"
          on:click={handleGoogleAuth}
          disabled={loading}
          class="w-full flex items-center justify-center px-4 py-3 border border-gray-700 rounded-lg
                 bg-white hover:bg-gray-50 text-gray-900 font-medium transition-all duration-200
                 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent
                 disabled:opacity-50 disabled:cursor-not-allowed transform hover:-translate-y-0.5 mb-6"
        >
          {#if loading}
            <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-900 mr-3"></div>
            Connecting...
          {:else}
            <svg class="w-5 h-5 mr-3" viewBox="0 0 24 24">
              <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
            Continue with Google
          {/if}
        </button>

        <!-- Error Display -->
        {#if error}
          <div class="mb-6 p-4 bg-red-900/20 border border-red-500/30 rounded-lg">
            <p class="text-red-400 text-sm">{error}</p>
          </div>
        {/if}

        <!-- Divider -->
        <div class="relative mb-6">
          <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-700"></div>
          </div>
          <div class="relative flex justify-center text-sm">
            <span class="px-2 bg-dark-lighter text-gray-400">Or continue with email</span>
          </div>
        </div>

        <!-- Email/Password Form -->
        <form on:submit|preventDefault={handleSubmit} class="space-y-6">
          <!-- Email Field -->
          <div>
            <label for="email" class="block text-sm font-medium text-gray-300 mb-2">
              Email address
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Mail class="h-5 w-5 text-gray-400" />
              </div>
              <input
                id="email"
                type="email"
                bind:value={email}
                required
                class="w-full pl-10 pr-4 py-3 bg-dark border border-gray-700 rounded-lg
                       text-white placeholder-gray-400 focus:outline-none focus:ring-2 
                       focus:ring-primary focus:border-transparent transition-all duration-200"
                placeholder="Enter your email"
              />
            </div>
          </div>

          <!-- Password Field -->
          <div>
            <label for="password" class="block text-sm font-medium text-gray-300 mb-2">
              Password
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Lock class="h-5 w-5 text-gray-400" />
              </div>
              {#if showPassword}
                <input
                  id="password"
                  type="text"
                  bind:value={password}
                  required
                  class="w-full pl-10 pr-12 py-3 bg-dark border border-gray-700 rounded-lg
                         text-white placeholder-gray-400 focus:outline-none focus:ring-2
                         focus:ring-primary focus:border-transparent transition-all duration-200"
                  placeholder="Enter your password"
                />
              {:else}
                <input
                  id="password"
                  type="password"
                  bind:value={password}
                  required
                  class="w-full pl-10 pr-12 py-3 bg-dark border border-gray-700 rounded-lg
                         text-white placeholder-gray-400 focus:outline-none focus:ring-2
                         focus:ring-primary focus:border-transparent transition-all duration-200"
                  placeholder="Enter your password"
                />
              {/if}
              <button
                type="button"
                class="absolute inset-y-0 right-0 pr-3 flex items-center"
                on:click={togglePasswordVisibility}
              >
                {#if showPassword}
                  <EyeOff class="h-5 w-5 text-gray-400 hover:text-gray-300" />
                {:else}
                  <Eye class="h-5 w-5 text-gray-400 hover:text-gray-300" />
                {/if}
              </button>
            </div>
          </div>

          <!-- Confirm Password (Sign Up Only) -->
          {#if !isLogin}
            <div transition:fly={{ y: -20, duration: 300 }}>
              <label for="confirmPassword" class="block text-sm font-medium text-gray-300 mb-2">
                Confirm password
              </label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock class="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="confirmPassword"
                  type="password"
                  bind:value={confirmPassword}
                  required
                  class="w-full pl-10 pr-4 py-3 bg-dark border border-gray-700 rounded-lg
                         text-white placeholder-gray-400 focus:outline-none focus:ring-2 
                         focus:ring-primary focus:border-transparent transition-all duration-200"
                  placeholder="Confirm your password"
                />
              </div>
            </div>
          {/if}

          <!-- Submit Button -->
          <Button
            type="submit"
            customClass="w-full py-3 text-lg font-semibold shadow-glow hover:shadow-lg transform hover:-translate-y-0.5 flex items-center justify-center"
            disabled={loading}
          >
            {#if loading}
              <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
              Processing...
            {:else}
              <span class="flex items-center">
                {isLogin ? 'Sign In' : 'Create Account'}
                <ArrowRight class="ml-2 h-5 w-5" />
              </span>
            {/if}
          </Button>

          <!-- Forgot Password Link (Sign In Only) -->
          {#if isLogin}
            <div class="mt-4 text-center">
              <button
                type="button"
                class="text-sm text-gray-400 hover:text-white transition-colors"
                on:click={showForgotPasswordForm}
              >
                Forgot your password?
              </button>
            </div>
          {/if}
        </form>

        <!-- Toggle Auth Mode -->
        <div class="mt-8 text-center">
          <p class="text-gray-400">
            {isLogin ? "Don't have an account?" : 'Already have an account?'}
            <button
              class="text-primary hover:text-primary-light font-medium ml-1 transition-colors duration-200"
              on:click={toggleAuthMode}
            >
              {isLogin ? 'Sign up' : 'Sign in'}
            </button>
          </p>
        </div>

        <!-- Trust Badge -->
        <div class="mt-6 text-center">
          <div class="flex items-center justify-center text-sm text-gray-500 mb-3">
            <Shield class="h-4 w-4 mr-1" />
            <span>Your data is secure and encrypted</span>
          </div>
          <div class="text-xs text-gray-500">
            By {isLogin ? 'signing in' : 'creating an account'}, you agree to our
            <button
              class="text-primary hover:text-primary-light underline"
              on:click={() => page.set('terms')}
            >
              Terms of Service
            </button>
            and
            <button
              class="text-primary hover:text-primary-light underline"
              on:click={() => page.set('privacy')}
            >
              Privacy Policy
            </button>
          </div>
        </div>
        {/if}
        {/if}
      </div>
    </div>
  </div>
</div>
{/if}
