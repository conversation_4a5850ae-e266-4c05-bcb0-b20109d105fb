<script lang="ts">
    import { getAllBlogPosts, getBlogPost } from '../data/blogPosts';
    import BlogCard from '../components/blog/BlogCard.svelte';
    import BlogPost from '../components/blog/BlogPost.svelte';
    
    export let slug: string | undefined = undefined;
    
    $: post = slug ? getBlogPost(slug) : undefined;
    $: posts = getAllBlogPosts();
  </script>
  
  <svelte:head>
    {#if !slug}
      <title>Blog | BargainHawk - Costco Price Tracking Tips & Walmart Savings Guides</title>
      <meta name="description" content="Learn how to save money on your Costco purchases with our expert tips and guides. Discover Walmart price adjustments, price tracking strategies and maximize your savings." />
      <meta name="keywords" content="costco price tracking, costco savings tips, costco price adjustments, costco shopping guide, costco.ca tips, walmart price tracking, walmart price adjustments, walmart price drop alerts, walmart savings, walmart deals" />
      <link rel="canonical" href="https://bargainhawk.ca/blog" />
    {/if}
  </svelte:head>
  
  <div class="min-h-screen bg-gray-50 pt-20">
    {#if post}
      <BlogPost {post} />
    {:else}
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="text-center mb-12">
          <h1 class="text-3xl font-bold text-gray-900 mb-4">Latest Articles</h1>
          <p class="text-lg text-gray-600 max-w-2xl mx-auto">
            Discover expert tips on price tracking, savings strategies, and deal hunting.
            <a href="/feed" class="text-primary hover:text-primary-dark underline">Browse current price drops</a> or
            <a href="/community" class="text-primary hover:text-primary-dark underline">join our community</a>
            to share your own money-saving discoveries.
          </p>
        </div>

        <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {#each posts as post}
            <BlogCard {post} />
          {/each}
        </div>

        <!-- Additional contextual links -->
        <div class="mt-16 text-center bg-gray-100 rounded-lg p-8">
          <h2 class="text-2xl font-bold text-gray-900 mb-4">Ready to Start Saving?</h2>
          <p class="text-gray-600 mb-6">
            Put these tips into action with our free price tracking tools.
          </p>
          <div class="flex flex-wrap justify-center gap-4">
            <a href="/auth" class="bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary-dark transition-colors">
              Get Started Free
            </a>
            <a href="/about" class="border border-primary text-primary px-6 py-3 rounded-lg hover:bg-primary hover:text-white transition-colors">
              Learn More
            </a>
            <a href="/faq" class="text-primary hover:text-primary-dark underline">
              Frequently Asked Questions
            </a>
          </div>
        </div>
      </div>
    {/if}
  </div>