<script lang="ts">
  import { onMount } from 'svelte';
  import { fade } from 'svelte/transition';

  let mounted = false;

  onMount(() => {
    mounted = true;
  });
</script>

<svelte:head>
  <title>Terms of Service | BargainHawk</title>
  <meta name="description" content="Terms of Service for BargainHawk - Price tracking and savings platform." />
</svelte:head>

{#if mounted}
<div class="min-h-screen gradient-bg" in:fade={{ duration: 600 }}>
  <div class="max-w-4xl mx-auto px-4 py-16">
    <!-- Header -->
    <div class="text-center mb-12">
      <div class="flex items-center justify-center mb-6">
        <img src="/BargainHawk.svg" alt="BargainHawk" class="h-12 w-12 mr-4" />
        <span class="text-3xl font-bold text-white">BargainHawk</span>
      </div>
      <h1 class="text-4xl font-bold text-white mb-4">Terms of Service</h1>
      <p class="text-gray-400 text-lg">Last updated: {new Date().toLocaleDateString()}</p>
    </div>

    <!-- Content -->
    <div class="bg-dark-lighter/50 backdrop-blur-xl rounded-2xl border border-gray-800/50 p-8 shadow-2xl">
      <div class="prose prose-invert max-w-none">
        
        <h2 class="text-2xl font-bold text-white mb-4">1. Acceptance of Terms</h2>
        <p class="text-gray-300 mb-6">
          By accessing and using BargainHawk ("the Service"), you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.
        </p>

        <h2 class="text-2xl font-bold text-white mb-4">2. Description of Service</h2>
        <p class="text-gray-300 mb-6">
          BargainHawk is a price tracking service that monitors product prices from various retailers, particularly Costco Canada, and notifies users of price changes and potential savings opportunities. The service includes features such as:
        </p>
        <ul class="text-gray-300 mb-6 list-disc list-inside space-y-2">
          <li>Automated price tracking</li>
          <li>Price drop notifications</li>
          <li>Price adjustment alerts</li>
          <li>Receipt upload and analysis</li>
          <li>Dashboard for managing tracked products</li>
        </ul>

        <h2 class="text-2xl font-bold text-white mb-4">3. User Accounts</h2>
        <p class="text-gray-300 mb-6">
          To access certain features of the Service, you may be required to create an account. You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account.
        </p>

        <h2 class="text-2xl font-bold text-white mb-4">4. Privacy and Data Collection</h2>
        <p class="text-gray-300 mb-6">
          We collect and process personal information in accordance with our Privacy Policy. By using the Service, you consent to the collection and use of information as outlined in our Privacy Policy.
        </p>

        <h2 class="text-2xl font-bold text-white mb-4">5. Acceptable Use</h2>
        <p class="text-gray-300 mb-4">You agree not to:</p>
        <ul class="text-gray-300 mb-6 list-disc list-inside space-y-2">
          <li>Use the Service for any unlawful purpose or in violation of any applicable laws</li>
          <li>Attempt to gain unauthorized access to the Service or its related systems</li>
          <li>Interfere with or disrupt the Service or servers connected to the Service</li>
          <li>Use automated systems to access the Service in a manner that sends more requests than a human can reasonably produce</li>
        </ul>

        <h2 class="text-2xl font-bold text-white mb-4">6. Disclaimers</h2>
        <p class="text-gray-300 mb-6">
          The Service is provided "as is" without any representations or warranties. We do not guarantee the accuracy, completeness, or timeliness of price information. Price tracking and notifications are provided on a best-effort basis.
        </p>

        <h2 class="text-2xl font-bold text-white mb-4">7. Limitation of Liability</h2>
        <p class="text-gray-300 mb-6">
          BargainHawk shall not be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses.
        </p>

        <h2 class="text-2xl font-bold text-white mb-4">8. Third-Party Services</h2>
        <p class="text-gray-300 mb-6">
          The Service may integrate with third-party services (such as Google for authentication). Your use of such third-party services is subject to their respective terms of service and privacy policies.
        </p>

        <h2 class="text-2xl font-bold text-white mb-4">9. Modifications to Terms</h2>
        <p class="text-gray-300 mb-6">
          We reserve the right to modify these terms at any time. We will notify users of any material changes via email or through the Service. Continued use of the Service after such modifications constitutes acceptance of the updated terms.
        </p>

        <h2 class="text-2xl font-bold text-white mb-4">10. Termination</h2>
        <p class="text-gray-300 mb-6">
          We may terminate or suspend your account and access to the Service immediately, without prior notice, for conduct that we believe violates these Terms of Service or is harmful to other users, us, or third parties.
        </p>

        <h2 class="text-2xl font-bold text-white mb-4">11. Governing Law</h2>
        <p class="text-gray-300 mb-6">
          These Terms shall be governed by and construed in accordance with the laws of Canada, without regard to its conflict of law provisions.
        </p>

        <h2 class="text-2xl font-bold text-white mb-4">12. Contact Information</h2>
        <p class="text-gray-300 mb-6">
          If you have any questions about these Terms of Service, please contact us through our website or support channels.
        </p>

        <div class="mt-12 p-6 bg-primary/10 rounded-lg border border-primary/20">
          <p class="text-primary font-medium">
            By using BargainHawk, you acknowledge that you have read, understood, and agree to be bound by these Terms of Service.
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
{/if}
