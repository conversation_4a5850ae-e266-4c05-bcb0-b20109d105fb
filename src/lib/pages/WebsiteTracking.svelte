<script lang="ts">
  import { onMount } from 'svelte';
  import { user } from '../stores/auth';
  import { supabase } from '../utils/supabase';
  import { Monitor, Plus, Settings, Eye, Camera } from 'lucide-svelte';
  import RectangleSelector from '../components/website-tracking/RectangleSelector.svelte';
  
  interface ScreenshotData {
    id: string;
    url: string;
    screenshotUrl: string;
    coordinates?: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
    createdAt: string;
    updatedAt: string;
  }
  
  let screenshots: ScreenshotData[] = [];
  let loading = false;
  let error = '';
  let newUrl = '';
  let showAddForm = false;
  let selectedScreenshot: ScreenshotData | null = null;
  let showRectangleSelector = false;

  // Rectangle selector state
  let isDrawing = false;
  let startX = 0;
  let startY = 0;
  let currentX = 0;
  let currentY = 0;
  let imageElement: HTMLImageElement;
  let isDragging = false;
  let dragHandle = '';
  let selectionRect = { x: 0, y: 0, width: 0, height: 0 };
  let dragStartX = 0;
  let dragStartY = 0;
  let initialRect = { x: 0, y: 0, width: 0, height: 0 };
  let autoSaveTimeout: ReturnType<typeof setTimeout>;
  
  const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8080';
  
  onMount(() => {
    loadScreenshots();

    // Global mouse event listeners for dragging outside the image
    const handleGlobalMouseMove = (event: MouseEvent) => {
      if ((isDrawing || isDragging) && imageElement) {
        handleMouseMove(event);
      }
    };

    const handleGlobalMouseUp = () => {
      if (isDrawing || isDragging) {
        handleMouseUp();
      }
    };

    document.addEventListener('mousemove', handleGlobalMouseMove);
    document.addEventListener('mouseup', handleGlobalMouseUp);

    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove);
      document.removeEventListener('mouseup', handleGlobalMouseUp);
      if (autoSaveTimeout) {
        clearTimeout(autoSaveTimeout);
      }
    };
  });
  
  async function loadScreenshots() {
    if (!$user) return;

    // Get the current session to access the token
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.access_token) return;

    loading = true;
    try {
      const response = await fetch(`${API_BASE_URL}/screenshots`, {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        screenshots = await response.json();
      } else {
        error = 'Failed to load screenshots';
      }
    } catch (err) {
      error = 'Network error loading screenshots';
      console.error('Error loading screenshots:', err);
    } finally {
      loading = false;
    }
  }
  
  async function captureScreenshot() {
    console.log('captureScreenshot called with URL:', newUrl);
    console.log('API_BASE_URL:', API_BASE_URL);

    if (!newUrl.trim()) {
      error = 'Please enter a valid URL';
      return;
    }

    if (!$user) {
      error = 'Authentication required';
      return;
    }

    // Get the current session to access the token
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.access_token) {
      error = 'Authentication required';
      return;
    }

    console.log('User authenticated, session exists:', !!session);

    loading = true;
    error = '';

    try {
      const requestUrl = `${API_BASE_URL}/screenshots?url=${encodeURIComponent(newUrl)}`;
      console.log('Making request to:', requestUrl);

      const response = await fetch(requestUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Response status:', response.status);
      console.log('Response ok:', response.ok);

      if (response.ok) {
        const result = await response.json();
        console.log('Response result:', result);

        if (result.status === 'success') {
          newUrl = '';
          showAddForm = false;
          await loadScreenshots();

          // Automatically open rectangle selector for the new screenshot
          if (screenshots.length > 0) {
            const newScreenshot = screenshots[screenshots.length - 1];
            openRectangleSelector(newScreenshot);
          }
        } else {
          error = result.message || 'Failed to capture screenshot';
        }
      } else {
        const errorText = await response.text();
        console.error('Response error:', errorText);
        error = `Failed to capture screenshot: ${response.status}`;
      }
    } catch (err) {
      error = 'Network error capturing screenshot';
      console.error('Error capturing screenshot:', err);
    } finally {
      loading = false;
    }
  }
  
  function openRectangleSelector(screenshot: ScreenshotData) {
    selectedScreenshot = screenshot;
    showRectangleSelector = true;

    // Initialize selection rectangle
    setTimeout(() => {
      if (imageElement) {
        const rect = imageElement.getBoundingClientRect();

        if (screenshot.coordinates) {
          // Use existing coordinates
          selectionRect = { ...screenshot.coordinates };
        } else {
          // Initialize to full screenshot
          selectionRect = {
            x: 0,
            y: 0,
            width: rect.width,
            height: rect.height
          };
        }

        // Set drawing coordinates to match selection
        startX = selectionRect.x;
        startY = selectionRect.y;
        currentX = selectionRect.x + selectionRect.width;
        currentY = selectionRect.y + selectionRect.height;
      }
    }, 100);
  }
  
  function closeRectangleSelector() {
    showRectangleSelector = false;
    selectedScreenshot = null;
    isDrawing = false;
    isDragging = false;
    dragHandle = '';
    selectionRect = { x: 0, y: 0, width: 0, height: 0 };
    dragStartX = 0;
    dragStartY = 0;
    initialRect = { x: 0, y: 0, width: 0, height: 0 };
  }

  function handleHandleMouseDown(event: MouseEvent, handle: string) {
    event.preventDefault();
    event.stopPropagation();

    if (!imageElement) return;

    const rect = imageElement.getBoundingClientRect();
    dragStartX = event.clientX - rect.left;
    dragStartY = event.clientY - rect.top;
    dragHandle = handle;
    isDragging = true;
    initialRect = { ...selectionRect };
  }

  function handleBoxMouseDown(event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();

    if (!imageElement) return;

    const rect = imageElement.getBoundingClientRect();
    dragStartX = event.clientX - rect.left;
    dragStartY = event.clientY - rect.top;
    dragHandle = 'move';
    isDragging = true;
    initialRect = { ...selectionRect };
  }
  
  function handleMouseDown(event: MouseEvent) {
    if (!imageElement || isDragging) return;

    event.preventDefault();
    const rect = imageElement.getBoundingClientRect();
    startX = Math.max(0, Math.min(event.clientX - rect.left, rect.width));
    startY = Math.max(0, Math.min(event.clientY - rect.top, rect.height));
    currentX = startX;
    currentY = startY;
    isDrawing = true;

    // Reset selection when starting new draw
    selectionRect = { x: 0, y: 0, width: 0, height: 0 };
  }
  
  function handleMouseMove(event: MouseEvent) {
    if ((!isDrawing && !isDragging) || !imageElement) return;

    event.preventDefault();
    const rect = imageElement.getBoundingClientRect();
    const mouseX = Math.max(0, Math.min(event.clientX - rect.left, rect.width));
    const mouseY = Math.max(0, Math.min(event.clientY - rect.top, rect.height));

    if (isDrawing) {
      // Regular drawing mode
      currentX = mouseX;
      currentY = mouseY;

      // Update selection rect for drawing
      selectionRect = {
        x: Math.min(startX, currentX),
        y: Math.min(startY, currentY),
        width: Math.abs(currentX - startX),
        height: Math.abs(currentY - startY)
      };
    } else if (isDragging) {
      // Handle dragging/resizing
      const deltaX = mouseX - dragStartX;
      const deltaY = mouseY - dragStartY;

      if (dragHandle === 'move') {
        // Move the entire box
        selectionRect = {
          x: Math.max(0, Math.min(initialRect.x + deltaX, rect.width - initialRect.width)),
          y: Math.max(0, Math.min(initialRect.y + deltaY, rect.height - initialRect.height)),
          width: initialRect.width,
          height: initialRect.height
        };
      } else {
        // Resize based on handle
        updateSelectionForHandle(deltaX, deltaY, rect);
      }
    }
  }

  function updateSelectionForHandle(deltaX: number, deltaY: number, imageRect: DOMRect) {
    const minSize = 10;
    let newRect = { ...initialRect };

    switch (dragHandle) {
      case 'nw': // Top-left
        newRect.x = Math.max(0, Math.min(initialRect.x + deltaX, initialRect.x + initialRect.width - minSize));
        newRect.y = Math.max(0, Math.min(initialRect.y + deltaY, initialRect.y + initialRect.height - minSize));
        newRect.width = initialRect.width - (newRect.x - initialRect.x);
        newRect.height = initialRect.height - (newRect.y - initialRect.y);
        break;
      case 'ne': // Top-right
        newRect.y = Math.max(0, Math.min(initialRect.y + deltaY, initialRect.y + initialRect.height - minSize));
        newRect.width = Math.max(minSize, Math.min(initialRect.width + deltaX, imageRect.width - initialRect.x));
        newRect.height = initialRect.height - (newRect.y - initialRect.y);
        break;
      case 'sw': // Bottom-left
        newRect.x = Math.max(0, Math.min(initialRect.x + deltaX, initialRect.x + initialRect.width - minSize));
        newRect.width = initialRect.width - (newRect.x - initialRect.x);
        newRect.height = Math.max(minSize, Math.min(initialRect.height + deltaY, imageRect.height - initialRect.y));
        break;
      case 'se': // Bottom-right
        newRect.width = Math.max(minSize, Math.min(initialRect.width + deltaX, imageRect.width - initialRect.x));
        newRect.height = Math.max(minSize, Math.min(initialRect.height + deltaY, imageRect.height - initialRect.y));
        break;
      case 'n': // Top edge
        newRect.y = Math.max(0, Math.min(initialRect.y + deltaY, initialRect.y + initialRect.height - minSize));
        newRect.height = initialRect.height - (newRect.y - initialRect.y);
        break;
      case 's': // Bottom edge
        newRect.height = Math.max(minSize, Math.min(initialRect.height + deltaY, imageRect.height - initialRect.y));
        break;
      case 'w': // Left edge
        newRect.x = Math.max(0, Math.min(initialRect.x + deltaX, initialRect.x + initialRect.width - minSize));
        newRect.width = initialRect.width - (newRect.x - initialRect.x);
        break;
      case 'e': // Right edge
        newRect.width = Math.max(minSize, Math.min(initialRect.width + deltaX, imageRect.width - initialRect.x));
        break;
    }

    selectionRect = newRect;
  }
  
  function handleMouseUp() {
    if (isDrawing) {
      isDrawing = false;
      // Auto-save after drawing
      scheduleAutoSave();
    }
    if (isDragging) {
      isDragging = false;
      dragHandle = '';
      // Auto-save after dragging/resizing
      scheduleAutoSave();
    }
  }

  function scheduleAutoSave() {
    // Clear any existing timeout
    if (autoSaveTimeout) {
      clearTimeout(autoSaveTimeout);
    }

    // Schedule auto-save after 500ms of inactivity
    autoSaveTimeout = setTimeout(() => {
      if (selectionRect.width >= 10 && selectionRect.height >= 10) {
        saveSelection(true); // Silent auto-save
      }
    }, 500);
  }

  async function saveSelection(silent = false) {
    if (!selectedScreenshot || !$user) return;

    // Get the current session to access the token
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.access_token) return;

    // Use current selection rectangle
    const { x, y, width, height } = selectionRect;

    // Ensure minimum selection size
    if (width < 10 || height < 10) {
      if (!silent) {
        error = 'Selection area is too small. Please select a larger area.';
      }
      return;
    }

    // Save coordinates
    try {
      const response = await fetch(`${API_BASE_URL}/screenshots/${selectedScreenshot.id}/coordinates`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ x, y, width, height })
      });

      if (response.ok) {
        await loadScreenshots();
        // Update the selected screenshot with new coordinates
        if (selectedScreenshot) {
          selectedScreenshot.coordinates = { x, y, width, height };
        }
        if (!silent) {
          error = ''; // Clear any previous errors only for manual saves
        }
      } else {
        if (!silent) {
          error = 'Failed to save coordinates';
        }
      }
    } catch (err) {
      if (!silent) {
        error = 'Network error saving coordinates';
      }
      console.error('Error saving coordinates:', err);
    }
  }
  

  
  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
</script>

<div class="min-h-screen bg-dark text-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
      <div class="flex items-center space-x-3">
        <Monitor class="h-8 w-8 text-primary" />
        <h1 class="text-3xl font-bold">Website Tracking</h1>
      </div>
      
      <button
        class="flex items-center space-x-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
        on:click={() => showAddForm = !showAddForm}
        disabled={loading}
      >
        <Plus class="h-5 w-5" />
        <span>Add Website</span>
      </button>
    </div>
    
    <!-- Add Website Form -->
    {#if showAddForm}
      <div class="bg-dark-lighter rounded-lg p-6 mb-8 border border-gray-800">
        <h2 class="text-xl font-semibold mb-4">Capture Website Screenshot</h2>
        <div class="flex space-x-4">
          <input
            type="url"
            bind:value={newUrl}
            placeholder="Enter website URL (e.g., https://example.com)"
            class="flex-1 px-4 py-2 bg-dark border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary"
            disabled={loading}
          />
          <button
            class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors disabled:opacity-50"
            on:click={captureScreenshot}
            disabled={loading || !newUrl.trim()}
          >
            {#if loading}
              <div class="flex items-center space-x-2">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Capturing...</span>
              </div>
            {:else}
              <div class="flex items-center space-x-2">
                <Camera class="h-4 w-4" />
                <span>Capture</span>
              </div>
            {/if}
          </button>
        </div>
      </div>
    {/if}
    
    <!-- Error Message -->
    {#if error}
      <div class="bg-red-900/50 border border-red-700 text-red-200 px-4 py-3 rounded-lg mb-6">
        {error}
      </div>
    {/if}
    
    <!-- Screenshots Grid -->
    {#if loading && screenshots.length === 0}
      <div class="flex items-center justify-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span class="ml-3 text-gray-400">Loading screenshots...</span>
      </div>
    {:else if screenshots.length === 0}
      <div class="text-center py-12">
        <Monitor class="h-16 w-16 text-gray-600 mx-auto mb-4" />
        <h3 class="text-xl font-semibold text-gray-400 mb-2">No websites tracked yet</h3>
        <p class="text-gray-500 mb-6">Start by adding a website to track changes</p>
        <button
          class="px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
          on:click={() => showAddForm = true}
        >
          Add Your First Website
        </button>
      </div>
    {:else}
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {#each screenshots as screenshot}
          <div class="bg-dark-lighter rounded-lg border border-gray-800 overflow-hidden">
            <!-- Screenshot Image -->
            <div class="aspect-video bg-gray-900 relative overflow-hidden">
              <img
                src={screenshot.screenshotUrl}
                alt="Screenshot of {screenshot.url}"
                class="w-full h-full object-cover"
                loading="lazy"
              />
            </div>
            
            <!-- Card Content -->
            <div class="p-4">
              <h3 class="font-semibold text-white mb-2 truncate" title={screenshot.url}>
                {screenshot.url}
              </h3>
              <p class="text-sm text-gray-400 mb-4">
                Captured {formatDate(screenshot.createdAt)}
              </p>
              
              <!-- Actions -->
              <div class="flex space-x-2">
                <button
                  class="flex-1 flex items-center justify-center space-x-2 px-3 py-2 bg-primary/20 text-primary rounded-lg hover:bg-primary/30 transition-colors"
                  on:click={() => openRectangleSelector(screenshot)}
                >
                  <Settings class="h-4 w-4" />
                  <span class="text-sm">
                    {screenshot.coordinates ? 'Edit Area' : 'Select Area'}
                  </span>
                </button>
                <button
                  class="px-3 py-2 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors"
                  on:click={() => window.open(screenshot.url, '_blank')}
                >
                  <Eye class="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        {/each}
      </div>
    {/if}
  </div>
</div>

<!-- Rectangle Selector Modal -->
<RectangleSelector
  screenshot={selectedScreenshot}
  show={showRectangleSelector}
  on:close={() => showRectangleSelector = false}
  on:saved={handleCoordinatesSaved}
/>
