<script lang="ts">
  import { onMount } from 'svelte';
  import { user } from '../stores/auth';
  import { supabase } from '../utils/supabase';
  import { Monitor, Plus, Settings, Eye, Camera, X } from 'lucide-svelte';
  
  interface ScreenshotData {
    id: string;
    url: string;
    screenshotUrl: string;
    coordinates?: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
    createdAt: string;
    updatedAt: string;
  }
  
  let screenshots: ScreenshotData[] = [];
  let loading = false;
  let error = '';
  let newUrl = '';
  let showAddForm = false;
  let selectedScreenshot: ScreenshotData | null = null;
  let showRectangleSelector = false;

  // Rectangle selector state
  let isDrawing = false;
  let startX = 0;
  let startY = 0;
  let currentX = 0;
  let currentY = 0;
  let imageElement: HTMLImageElement;
  let isDragging = false;
  let dragHandle = '';
  let selectionRect = { x: 0, y: 0, width: 0, height: 0 };
  
  const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8080';
  
  onMount(() => {
    loadScreenshots();
  });
  
  async function loadScreenshots() {
    if (!$user) return;

    // Get the current session to access the token
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.access_token) return;

    loading = true;
    try {
      const response = await fetch(`${API_BASE_URL}/screenshots`, {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        screenshots = await response.json();
      } else {
        error = 'Failed to load screenshots';
      }
    } catch (err) {
      error = 'Network error loading screenshots';
      console.error('Error loading screenshots:', err);
    } finally {
      loading = false;
    }
  }
  
  async function captureScreenshot() {
    console.log('captureScreenshot called with URL:', newUrl);
    console.log('API_BASE_URL:', API_BASE_URL);

    if (!newUrl.trim()) {
      error = 'Please enter a valid URL';
      return;
    }

    if (!$user) {
      error = 'Authentication required';
      return;
    }

    // Get the current session to access the token
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.access_token) {
      error = 'Authentication required';
      return;
    }

    console.log('User authenticated, session exists:', !!session);

    loading = true;
    error = '';

    try {
      const requestUrl = `${API_BASE_URL}/screenshots?url=${encodeURIComponent(newUrl)}`;
      console.log('Making request to:', requestUrl);

      const response = await fetch(requestUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Response status:', response.status);
      console.log('Response ok:', response.ok);

      if (response.ok) {
        const result = await response.json();
        console.log('Response result:', result);

        if (result.status === 'success') {
          newUrl = '';
          showAddForm = false;
          await loadScreenshots();

          // Automatically open rectangle selector for the new screenshot
          if (screenshots.length > 0) {
            const newScreenshot = screenshots[screenshots.length - 1];
            openRectangleSelector(newScreenshot);
          }
        } else {
          error = result.message || 'Failed to capture screenshot';
        }
      } else {
        const errorText = await response.text();
        console.error('Response error:', errorText);
        error = `Failed to capture screenshot: ${response.status}`;
      }
    } catch (err) {
      error = 'Network error capturing screenshot';
      console.error('Error capturing screenshot:', err);
    } finally {
      loading = false;
    }
  }
  
  function openRectangleSelector(screenshot: ScreenshotData) {
    selectedScreenshot = screenshot;
    showRectangleSelector = true;

    // Initialize selection to full screenshot if no coordinates exist
    if (!screenshot.coordinates) {
      // We'll set the full dimensions once the image loads
      setTimeout(() => {
        if (imageElement) {
          const rect = imageElement.getBoundingClientRect();
          selectionRect = {
            x: 0,
            y: 0,
            width: imageElement.naturalWidth,
            height: imageElement.naturalHeight
          };

          // Scale to display size
          const scaleX = rect.width / imageElement.naturalWidth;
          const scaleY = rect.height / imageElement.naturalHeight;

          startX = 0;
          startY = 0;
          currentX = rect.width;
          currentY = rect.height;
        }
      }, 100);
    }
  }
  
  function closeRectangleSelector() {
    showRectangleSelector = false;
    selectedScreenshot = null;
    isDrawing = false;
    isDragging = false;
    dragHandle = '';
    selectionRect = { x: 0, y: 0, width: 0, height: 0 };
  }
  
  function handleMouseDown(event: MouseEvent) {
    if (!imageElement) return;

    event.preventDefault();
    const rect = imageElement.getBoundingClientRect();
    startX = Math.max(0, Math.min(event.clientX - rect.left, rect.width));
    startY = Math.max(0, Math.min(event.clientY - rect.top, rect.height));
    currentX = startX;
    currentY = startY;
    isDrawing = true;
  }
  
  function handleMouseMove(event: MouseEvent) {
    if (!isDrawing || !imageElement) return;
    
    const rect = imageElement.getBoundingClientRect();
    currentX = event.clientX - rect.left;
    currentY = event.clientY - rect.top;
  }
  
  async function handleMouseUp() {
    if (!isDrawing || !selectedScreenshot || !$user) return;

    // Get the current session to access the token
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.access_token) return;

    isDrawing = false;

    // Calculate rectangle coordinates
    const x = Math.min(startX, currentX);
    const y = Math.min(startY, currentY);
    const width = Math.abs(currentX - startX);
    const height = Math.abs(currentY - startY);

    // Save coordinates
    try {
      const response = await fetch(`${API_BASE_URL}/screenshots/${selectedScreenshot.id}/coordinates`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ x, y, width, height })
      });

      if (response.ok) {
        await loadScreenshots();
        closeRectangleSelector();
      } else {
        error = 'Failed to save coordinates';
      }
    } catch (err) {
      error = 'Network error saving coordinates';
      console.error('Error saving coordinates:', err);
    }
  }
  

  
  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
</script>

<div class="min-h-screen bg-dark text-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
      <div class="flex items-center space-x-3">
        <Monitor class="h-8 w-8 text-primary" />
        <h1 class="text-3xl font-bold">Website Tracking</h1>
      </div>
      
      <button
        class="flex items-center space-x-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
        on:click={() => showAddForm = !showAddForm}
        disabled={loading}
      >
        <Plus class="h-5 w-5" />
        <span>Add Website</span>
      </button>
    </div>
    
    <!-- Add Website Form -->
    {#if showAddForm}
      <div class="bg-dark-lighter rounded-lg p-6 mb-8 border border-gray-800">
        <h2 class="text-xl font-semibold mb-4">Capture Website Screenshot</h2>
        <div class="flex space-x-4">
          <input
            type="url"
            bind:value={newUrl}
            placeholder="Enter website URL (e.g., https://example.com)"
            class="flex-1 px-4 py-2 bg-dark border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary"
            disabled={loading}
          />
          <button
            class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors disabled:opacity-50"
            on:click={captureScreenshot}
            disabled={loading || !newUrl.trim()}
          >
            {#if loading}
              <div class="flex items-center space-x-2">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Capturing...</span>
              </div>
            {:else}
              <div class="flex items-center space-x-2">
                <Camera class="h-4 w-4" />
                <span>Capture</span>
              </div>
            {/if}
          </button>
        </div>
      </div>
    {/if}
    
    <!-- Error Message -->
    {#if error}
      <div class="bg-red-900/50 border border-red-700 text-red-200 px-4 py-3 rounded-lg mb-6">
        {error}
      </div>
    {/if}
    
    <!-- Screenshots Grid -->
    {#if loading && screenshots.length === 0}
      <div class="flex items-center justify-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span class="ml-3 text-gray-400">Loading screenshots...</span>
      </div>
    {:else if screenshots.length === 0}
      <div class="text-center py-12">
        <Monitor class="h-16 w-16 text-gray-600 mx-auto mb-4" />
        <h3 class="text-xl font-semibold text-gray-400 mb-2">No websites tracked yet</h3>
        <p class="text-gray-500 mb-6">Start by adding a website to track changes</p>
        <button
          class="px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
          on:click={() => showAddForm = true}
        >
          Add Your First Website
        </button>
      </div>
    {:else}
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {#each screenshots as screenshot}
          <div class="bg-dark-lighter rounded-lg border border-gray-800 overflow-hidden">
            <!-- Screenshot Image -->
            <div class="aspect-video bg-gray-900 relative">
              <img
                src={screenshot.screenshotUrl}
                alt="Screenshot of {screenshot.url}"
                class="w-full h-full object-cover"
                loading="lazy"
              />
              {#if screenshot.coordinates}
                <div
                  class="absolute border-2 border-primary bg-primary/20"
                  style="left: {screenshot.coordinates.x}px; top: {screenshot.coordinates.y}px; width: {screenshot.coordinates.width}px; height: {screenshot.coordinates.height}px;"
                ></div>
              {/if}
            </div>
            
            <!-- Card Content -->
            <div class="p-4">
              <h3 class="font-semibold text-white mb-2 truncate" title={screenshot.url}>
                {screenshot.url}
              </h3>
              <p class="text-sm text-gray-400 mb-4">
                Captured {formatDate(screenshot.createdAt)}
              </p>
              
              <!-- Actions -->
              <div class="flex space-x-2">
                <button
                  class="flex-1 flex items-center justify-center space-x-2 px-3 py-2 bg-primary/20 text-primary rounded-lg hover:bg-primary/30 transition-colors"
                  on:click={() => openRectangleSelector(screenshot)}
                >
                  <Settings class="h-4 w-4" />
                  <span class="text-sm">
                    {screenshot.coordinates ? 'Edit Area' : 'Select Area'}
                  </span>
                </button>
                <button
                  class="px-3 py-2 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors"
                  on:click={() => window.open(screenshot.url, '_blank')}
                >
                  <Eye class="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        {/each}
      </div>
    {/if}
  </div>
</div>

<!-- Rectangle Selector Modal -->
{#if showRectangleSelector && selectedScreenshot}
  <div class="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4">
    <div class="bg-dark-lighter rounded-lg max-w-6xl w-full max-h-[95vh] overflow-auto">
      <div class="p-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-xl font-semibold">Select Area to Monitor</h2>
          <button
            class="text-gray-400 hover:text-white transition-colors"
            on:click={closeRectangleSelector}
          >
            <X class="h-6 w-6" />
          </button>
        </div>

        <p class="text-gray-400 mb-6">
          Click and drag to select the area you want to monitor for changes. The selected area will be highlighted with a blue border.
        </p>

        <div class="relative inline-block bg-gray-900 rounded-lg overflow-hidden">
          <!-- Image Container with Overlay -->
          <div class="relative">
            <img
              bind:this={imageElement}
              src={selectedScreenshot.screenshotUrl}
              alt="Screenshot"
              class="max-w-full h-auto block"
            />

            <!-- Overlay Container -->
            <div
              class="absolute inset-0 cursor-crosshair"
              on:mousedown={handleMouseDown}
              on:mousemove={handleMouseMove}
              on:mouseup={handleMouseUp}
              role="button"
              tabindex="0"
              aria-label="Select area to monitor"
            >
              <!-- Shaded overlay for unselected areas -->
              {#if isDrawing || selectedScreenshot.coordinates}
                {@const rect = isDrawing ? {
                  x: Math.min(startX, currentX),
                  y: Math.min(startY, currentY),
                  width: Math.abs(currentX - startX),
                  height: Math.abs(currentY - startY)
                } : selectedScreenshot.coordinates}

                {#if rect && imageElement}
                  <!-- Top overlay -->
                  <div
                    class="absolute bg-black/60 pointer-events-none"
                    style="left: 0; top: 0; width: 100%; height: {rect.y}px;"
                  ></div>

                  <!-- Bottom overlay -->
                  <div
                    class="absolute bg-black/60 pointer-events-none"
                    style="left: 0; top: {rect.y + rect.height}px; width: 100%; height: calc(100% - {rect.y + rect.height}px);"
                  ></div>

                  <!-- Left overlay -->
                  <div
                    class="absolute bg-black/60 pointer-events-none"
                    style="left: 0; top: {rect.y}px; width: {rect.x}px; height: {rect.height}px;"
                  ></div>

                  <!-- Right overlay -->
                  <div
                    class="absolute bg-black/60 pointer-events-none"
                    style="left: {rect.x + rect.width}px; top: {rect.y}px; width: calc(100% - {rect.x + rect.width}px); height: {rect.height}px;"
                  ></div>

                  <!-- Selection rectangle with bold blue border -->
                  <div
                    class="absolute border-4 border-blue-500 bg-blue-500/10 pointer-events-none shadow-lg"
                    style="left: {rect.x}px; top: {rect.y}px; width: {rect.width}px; height: {rect.height}px;"
                  >
                    <!-- Corner handles for visual feedback -->
                    <div class="absolute -top-2 -left-2 w-4 h-4 bg-blue-500 border-2 border-white rounded-full"></div>
                    <div class="absolute -top-2 -right-2 w-4 h-4 bg-blue-500 border-2 border-white rounded-full"></div>
                    <div class="absolute -bottom-2 -left-2 w-4 h-4 bg-blue-500 border-2 border-white rounded-full"></div>
                    <div class="absolute -bottom-2 -right-2 w-4 h-4 bg-blue-500 border-2 border-white rounded-full"></div>

                    <!-- Edge handles -->
                    <div class="absolute -top-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-blue-500 border-2 border-white rounded-full"></div>
                    <div class="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-blue-500 border-2 border-white rounded-full"></div>
                    <div class="absolute -left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 bg-blue-500 border-2 border-white rounded-full"></div>
                    <div class="absolute -right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 bg-blue-500 border-2 border-white rounded-full"></div>
                  </div>
                {/if}
              {/if}
            </div>
          </div>
        </div>

        <!-- Action buttons -->
        <div class="flex justify-end space-x-3 mt-6">
          <button
            class="px-4 py-2 text-gray-400 hover:text-white transition-colors"
            on:click={closeRectangleSelector}
          >
            Cancel
          </button>
          <button
            class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
            on:click={handleMouseUp}
            disabled={!isDrawing && !selectedScreenshot.coordinates}
          >
            Save Selection
          </button>
        </div>
      </div>
    </div>
  </div>
{/if}
