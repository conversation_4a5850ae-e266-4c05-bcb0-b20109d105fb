<script lang="ts">
  import { onMount } from 'svelte';
  import { user } from '../stores/auth';
  import { Monitor, Plus, Settings, Trash2, Eye, Camera, X } from 'lucide-svelte';
  
  interface ScreenshotData {
    id: string;
    url: string;
    screenshotUrl: string;
    coordinates?: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
    createdAt: string;
    updatedAt: string;
  }
  
  let screenshots: ScreenshotData[] = [];
  let loading = false;
  let error = '';
  let newUrl = '';
  let showAddForm = false;
  let selectedScreenshot: ScreenshotData | null = null;
  let showRectangleSelector = false;
  
  // Rectangle selector state
  let isDrawing = false;
  let startX = 0;
  let startY = 0;
  let currentX = 0;
  let currentY = 0;
  let imageElement: HTMLImageElement;
  
  const API_BASE_URL = import.meta.env.VITE_URL || 'http://localhost:8080';
  
  onMount(() => {
    loadScreenshots();
  });
  
  async function loadScreenshots() {
    if (!$user?.access_token) return;
    
    loading = true;
    try {
      const response = await fetch(`${API_BASE_URL}/screenshots`, {
        headers: {
          'Authorization': `Bearer ${$user.access_token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        screenshots = await response.json();
      } else {
        error = 'Failed to load screenshots';
      }
    } catch (err) {
      error = 'Network error loading screenshots';
      console.error('Error loading screenshots:', err);
    } finally {
      loading = false;
    }
  }
  
  async function captureScreenshot() {
    if (!newUrl.trim() || !$user?.access_token) return;
    
    loading = true;
    error = '';
    
    try {
      const response = await fetch(`${API_BASE_URL}/screenshots?url=${encodeURIComponent(newUrl)}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${$user.access_token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.status === 'success') {
          newUrl = '';
          showAddForm = false;
          await loadScreenshots();
        } else {
          error = result.message || 'Failed to capture screenshot';
        }
      } else {
        error = 'Failed to capture screenshot';
      }
    } catch (err) {
      error = 'Network error capturing screenshot';
      console.error('Error capturing screenshot:', err);
    } finally {
      loading = false;
    }
  }
  
  function openRectangleSelector(screenshot: ScreenshotData) {
    selectedScreenshot = screenshot;
    showRectangleSelector = true;
  }
  
  function closeRectangleSelector() {
    showRectangleSelector = false;
    selectedScreenshot = null;
    isDrawing = false;
  }
  
  function handleMouseDown(event: MouseEvent) {
    if (!imageElement) return;
    
    const rect = imageElement.getBoundingClientRect();
    startX = event.clientX - rect.left;
    startY = event.clientY - rect.top;
    currentX = startX;
    currentY = startY;
    isDrawing = true;
  }
  
  function handleMouseMove(event: MouseEvent) {
    if (!isDrawing || !imageElement) return;
    
    const rect = imageElement.getBoundingClientRect();
    currentX = event.clientX - rect.left;
    currentY = event.clientY - rect.top;
  }
  
  async function handleMouseUp() {
    if (!isDrawing || !selectedScreenshot || !$user?.access_token) return;
    
    isDrawing = false;
    
    // Calculate rectangle coordinates
    const x = Math.min(startX, currentX);
    const y = Math.min(startY, currentY);
    const width = Math.abs(currentX - startX);
    const height = Math.abs(currentY - startY);
    
    // Save coordinates
    try {
      const response = await fetch(`${API_BASE_URL}/screenshots/${selectedScreenshot.id}/coordinates`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${$user.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ x, y, width, height })
      });
      
      if (response.ok) {
        await loadScreenshots();
        closeRectangleSelector();
      } else {
        error = 'Failed to save coordinates';
      }
    } catch (err) {
      error = 'Network error saving coordinates';
      console.error('Error saving coordinates:', err);
    }
  }
  
  function getRectangleStyle() {
    if (!isDrawing) return '';
    
    const x = Math.min(startX, currentX);
    const y = Math.min(startY, currentY);
    const width = Math.abs(currentX - startX);
    const height = Math.abs(currentY - startY);
    
    return `left: ${x}px; top: ${y}px; width: ${width}px; height: ${height}px;`;
  }
  
  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
</script>

<div class="min-h-screen bg-dark text-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
      <div class="flex items-center space-x-3">
        <Monitor class="h-8 w-8 text-primary" />
        <h1 class="text-3xl font-bold">Website Tracking</h1>
      </div>
      
      <button
        class="flex items-center space-x-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
        on:click={() => showAddForm = !showAddForm}
        disabled={loading}
      >
        <Plus class="h-5 w-5" />
        <span>Add Website</span>
      </button>
    </div>
    
    <!-- Add Website Form -->
    {#if showAddForm}
      <div class="bg-dark-lighter rounded-lg p-6 mb-8 border border-gray-800">
        <h2 class="text-xl font-semibold mb-4">Capture Website Screenshot</h2>
        <div class="flex space-x-4">
          <input
            type="url"
            bind:value={newUrl}
            placeholder="Enter website URL (e.g., https://example.com)"
            class="flex-1 px-4 py-2 bg-dark border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary"
            disabled={loading}
          />
          <button
            class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors disabled:opacity-50"
            on:click={captureScreenshot}
            disabled={loading || !newUrl.trim()}
          >
            {#if loading}
              <div class="flex items-center space-x-2">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Capturing...</span>
              </div>
            {:else}
              <div class="flex items-center space-x-2">
                <Camera class="h-4 w-4" />
                <span>Capture</span>
              </div>
            {/if}
          </button>
        </div>
      </div>
    {/if}
    
    <!-- Error Message -->
    {#if error}
      <div class="bg-red-900/50 border border-red-700 text-red-200 px-4 py-3 rounded-lg mb-6">
        {error}
      </div>
    {/if}
    
    <!-- Screenshots Grid -->
    {#if loading && screenshots.length === 0}
      <div class="flex items-center justify-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span class="ml-3 text-gray-400">Loading screenshots...</span>
      </div>
    {:else if screenshots.length === 0}
      <div class="text-center py-12">
        <Monitor class="h-16 w-16 text-gray-600 mx-auto mb-4" />
        <h3 class="text-xl font-semibold text-gray-400 mb-2">No websites tracked yet</h3>
        <p class="text-gray-500 mb-6">Start by adding a website to track changes</p>
        <button
          class="px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
          on:click={() => showAddForm = true}
        >
          Add Your First Website
        </button>
      </div>
    {:else}
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {#each screenshots as screenshot}
          <div class="bg-dark-lighter rounded-lg border border-gray-800 overflow-hidden">
            <!-- Screenshot Image -->
            <div class="aspect-video bg-gray-900 relative">
              <img
                src={screenshot.screenshotUrl}
                alt="Screenshot of {screenshot.url}"
                class="w-full h-full object-cover"
                loading="lazy"
              />
              {#if screenshot.coordinates}
                <div
                  class="absolute border-2 border-primary bg-primary/20"
                  style="left: {screenshot.coordinates.x}px; top: {screenshot.coordinates.y}px; width: {screenshot.coordinates.width}px; height: {screenshot.coordinates.height}px;"
                ></div>
              {/if}
            </div>
            
            <!-- Card Content -->
            <div class="p-4">
              <h3 class="font-semibold text-white mb-2 truncate" title={screenshot.url}>
                {screenshot.url}
              </h3>
              <p class="text-sm text-gray-400 mb-4">
                Captured {formatDate(screenshot.createdAt)}
              </p>
              
              <!-- Actions -->
              <div class="flex space-x-2">
                <button
                  class="flex-1 flex items-center justify-center space-x-2 px-3 py-2 bg-primary/20 text-primary rounded-lg hover:bg-primary/30 transition-colors"
                  on:click={() => openRectangleSelector(screenshot)}
                >
                  <Settings class="h-4 w-4" />
                  <span class="text-sm">
                    {screenshot.coordinates ? 'Edit Area' : 'Select Area'}
                  </span>
                </button>
                <button
                  class="px-3 py-2 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors"
                  on:click={() => window.open(screenshot.url, '_blank')}
                >
                  <Eye class="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        {/each}
      </div>
    {/if}
  </div>
</div>

<!-- Rectangle Selector Modal -->
{#if showRectangleSelector && selectedScreenshot}
  <div class="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4">
    <div class="bg-dark-lighter rounded-lg max-w-4xl w-full max-h-[90vh] overflow-auto">
      <div class="p-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-xl font-semibold">Select Area to Monitor</h2>
          <button
            class="text-gray-400 hover:text-white"
            on:click={closeRectangleSelector}
          >
            <X class="h-6 w-6" />
          </button>
        </div>
        
        <p class="text-gray-400 mb-4">
          Click and drag to select the area you want to monitor for changes.
        </p>
        
        <div class="relative inline-block">
          <img
            bind:this={imageElement}
            src={selectedScreenshot.screenshotUrl}
            alt="Screenshot"
            class="max-w-full h-auto cursor-crosshair"
            on:mousedown={handleMouseDown}
            on:mousemove={handleMouseMove}
            on:mouseup={handleMouseUp}
          />
          
          {#if isDrawing}
            <div
              class="absolute border-2 border-primary bg-primary/20 pointer-events-none"
              style={getRectangleStyle()}
            ></div>
          {/if}
          
          {#if selectedScreenshot.coordinates && !isDrawing}
            <div
              class="absolute border-2 border-primary bg-primary/20"
              style="left: {selectedScreenshot.coordinates.x}px; top: {selectedScreenshot.coordinates.y}px; width: {selectedScreenshot.coordinates.width}px; height: {selectedScreenshot.coordinates.height}px;"
            ></div>
          {/if}
        </div>
      </div>
    </div>
  </div>
{/if}
