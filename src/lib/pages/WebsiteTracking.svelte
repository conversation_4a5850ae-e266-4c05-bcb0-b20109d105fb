<script lang="ts">
  import { onMount } from 'svelte';
  import { user } from '../stores/auth';
  import { supabase } from '../utils/supabase';
  import { Monitor, Plus, Settings, Eye, Camera, X, Lock, Trash2, ExternalLink } from 'lucide-svelte';
  import WebsiteTrackingLimitModal from '../components/website-tracking/WebsiteTrackingLimitModal.svelte';
  
  interface ScreenshotData {
    id: string;
    supabaseId: string;
    url: string;
    screenshotUrl: string;
    coordinates?: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
    // Percentage-based coordinates for responsive display
    percentageCoordinates?: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
    createdAt: string;
    updatedAt: string;
  }
  
  let screenshots: ScreenshotData[] = [];
  let loading = false;
  let error = '';
  let newUrl = '';
  let showAddForm = false;
  let selectedScreenshot: ScreenshotData | null = null;
  let showRectangleSelector = false;
  let showWebsiteTrackingLimitModal = false;

  // Website tracking limit check
  $: isAtWebsiteTrackingLimit = screenshots.length >= 4;

  // Rectangle selector state
  let isDrawing = false;
  let startX = 0;
  let startY = 0;
  let currentX = 0;
  let currentY = 0;
  let imageElement: HTMLImageElement;
  let isDragging = false;
  let dragHandle = '';
  let selectionRect = { x: 0, y: 0, width: 0, height: 0 };
  let dragStartX = 0;
  let dragStartY = 0;
  let initialRect = { x: 0, y: 0, width: 0, height: 0 };
  let autoSaveTimeout: ReturnType<typeof setTimeout>;

  // Helper functions for percentage-based coordinates
  function pixelsToPercentage(pixelCoords: {x: number, y: number, width: number, height: number}, imageRect: DOMRect) {
    return {
      x: (pixelCoords.x / imageRect.width) * 100,
      y: (pixelCoords.y / imageRect.height) * 100,
      width: (pixelCoords.width / imageRect.width) * 100,
      height: (pixelCoords.height / imageRect.height) * 100
    };
  }

  function percentageToPixels(percentCoords: {x: number, y: number, width: number, height: number}, imageRect: DOMRect) {
    return {
      x: (percentCoords.x / 100) * imageRect.width,
      y: (percentCoords.y / 100) * imageRect.height,
      width: (percentCoords.width / 100) * imageRect.width,
      height: (percentCoords.height / 100) * imageRect.height
    };
  }
  
  const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8080';

  function handleAddWebsiteClick() {
    if (isAtWebsiteTrackingLimit) {
      showWebsiteTrackingLimitModal = true;
    } else {
      showAddForm = true;
    }
  }

  onMount(() => {
    loadScreenshots();

    // Global mouse and touch event listeners for dragging outside the image
    const handleGlobalMouseMove = (event: MouseEvent) => {
      if ((isDrawing || isDragging) && imageElement) {
        handleMouseMove(event);
      }
    };

    const handleGlobalTouchMove = (event: TouchEvent) => {
      if ((isDrawing || isDragging) && imageElement) {
        event.preventDefault(); // Prevent scrolling
        const touch = event.touches[0];
        const mouseEvent = new MouseEvent('mousemove', {
          clientX: touch.clientX,
          clientY: touch.clientY
        });
        handleMouseMove(mouseEvent);
      }
    };

    const handleGlobalMouseUp = () => {
      if (isDrawing || isDragging) {
        handleMouseUp();
      }
    };

    const handleGlobalTouchEnd = () => {
      if (isDrawing || isDragging) {
        handleMouseUp();
      }
    };

    document.addEventListener('mousemove', handleGlobalMouseMove);
    document.addEventListener('mouseup', handleGlobalMouseUp);
    document.addEventListener('touchmove', handleGlobalTouchMove, { passive: false });
    document.addEventListener('touchend', handleGlobalTouchEnd);

    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove);
      document.removeEventListener('mouseup', handleGlobalMouseUp);
      document.removeEventListener('touchmove', handleGlobalTouchMove);
      document.removeEventListener('touchend', handleGlobalTouchEnd);
      if (autoSaveTimeout) {
        clearTimeout(autoSaveTimeout);
      }
    };
  });
  
  async function loadScreenshots() {
    if (!$user) return;

    // Get the current session to access the token
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.access_token) return;

    loading = true;
    try {
      const response = await fetch(`${API_BASE_URL}/screenshots`, {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const rawScreenshots = await response.json();
        // Map backend response to frontend interface
        screenshots = rawScreenshots.map((screenshot: any) => ({
          ...screenshot,
          percentageCoordinates: screenshot.coordinates ? {
            x: screenshot.coordinates.percentageX || 0,
            y: screenshot.coordinates.percentageY || 0,
            width: screenshot.coordinates.percentageWidth || 100,
            height: screenshot.coordinates.percentageHeight || 100
          } : undefined
        }));
      } else {
        error = 'Failed to load screenshots';
      }
    } catch (err) {
      error = 'Network error loading screenshots';
      console.error('Error loading screenshots:', err);
    } finally {
      loading = false;
    }
  }

  // Generate UUID for Supabase storage organization
  function generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  async function captureScreenshot() {
    console.log('captureScreenshot called with URL:', newUrl);
    console.log('API_BASE_URL:', API_BASE_URL);

    if (!newUrl.trim()) {
      error = 'Please enter a valid URL';
      return;
    }

    if (!$user) {
      error = 'Authentication required';
      return;
    }

    // Get the current session to access the token
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.access_token) {
      error = 'Authentication required';
      return;
    }

    console.log('User authenticated, session exists:', !!session);

    loading = true;
    error = '';

    try {
      // Generate UUID for Supabase storage organization
      const supabaseId = generateUUID();

      const requestUrl = `${API_BASE_URL}/screenshots?url=${encodeURIComponent(newUrl)}&supabaseId=${encodeURIComponent(supabaseId)}`;
      console.log('Making request to:', requestUrl);

      const response = await fetch(requestUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Response status:', response.status);
      console.log('Response ok:', response.ok);

      if (response.ok) {
        const result = await response.json();
        console.log('Response result:', result);

        if (result.status === 'success') {
          newUrl = '';
          showAddForm = false;
          await loadScreenshots();

          // Automatically open rectangle selector for the new screenshot
          if (screenshots.length > 0) {
            const newScreenshot = screenshots[screenshots.length - 1];
            openRectangleSelector(newScreenshot);
          }
        } else {
          error = result.message || 'Failed to capture screenshot';
        }
      } else {
        const errorText = await response.text();
        console.error('Response error:', errorText);
        error = `Failed to capture screenshot: ${response.status}`;
      }
    } catch (err) {
      error = 'Network error capturing screenshot';
      console.error('Error capturing screenshot:', err);
    } finally {
      loading = false;
    }
  }

  async function deleteScreenshot(screenshotId: string) {
    if (!$user) return;

    // Get the current session to access the token
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.access_token) return;

    try {
      const response = await fetch(`${API_BASE_URL}/screenshots/${screenshotId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        // Remove the screenshot from the local list
        screenshots = screenshots.filter(s => s.id !== screenshotId);
      } else {
        error = 'Failed to delete website tracking';
      }
    } catch (err) {
      error = 'Network error deleting website tracking';
      console.error('Error deleting screenshot:', err);
    }
  }

  function openRectangleSelector(screenshot: ScreenshotData) {
    selectedScreenshot = screenshot;
    showRectangleSelector = true;

    // Initialize selection rectangle
    setTimeout(() => {
      if (imageElement) {
        const rect = imageElement.getBoundingClientRect();

        if (screenshot.percentageCoordinates) {
          // Convert percentage coordinates to pixels for current image size
          selectionRect = percentageToPixels(screenshot.percentageCoordinates, rect);
        } else if (screenshot.coordinates) {
          // Legacy: use existing pixel coordinates but convert to percentage for future saves
          selectionRect = { ...screenshot.coordinates };
        } else {
          // Initialize to center 50% of the screenshot
          const centerWidth = rect.width * 0.5;
          const centerHeight = rect.height * 0.5;
          const centerX = (rect.width - centerWidth) / 2;
          const centerY = (rect.height - centerHeight) / 2;

          selectionRect = {
            x: centerX,
            y: centerY,
            width: centerWidth,
            height: centerHeight
          };
        }

        // Set drawing coordinates to match selection
        startX = selectionRect.x;
        startY = selectionRect.y;
        currentX = selectionRect.x + selectionRect.width;
        currentY = selectionRect.y + selectionRect.height;
      }
    }, 100);
  }
  
  function closeRectangleSelector() {
    showRectangleSelector = false;
    selectedScreenshot = null;
    isDrawing = false;
    isDragging = false;
    dragHandle = '';
    selectionRect = { x: 0, y: 0, width: 0, height: 0 };
    dragStartX = 0;
    dragStartY = 0;
    initialRect = { x: 0, y: 0, width: 0, height: 0 };
  }

  function handleHandleMouseDown(event: MouseEvent | TouchEvent, handle: string) {
    event.preventDefault();
    event.stopPropagation();

    if (!imageElement) return;

    const rect = imageElement.getBoundingClientRect();
    const clientX = 'touches' in event ? event.touches[0].clientX : event.clientX;
    const clientY = 'touches' in event ? event.touches[0].clientY : event.clientY;

    dragStartX = clientX - rect.left;
    dragStartY = clientY - rect.top;
    dragHandle = handle;
    isDragging = true;
    initialRect = { ...selectionRect };
  }

  function handleBoxMouseDown(event: MouseEvent | TouchEvent) {
    event.preventDefault();
    event.stopPropagation();

    if (!imageElement) return;

    const rect = imageElement.getBoundingClientRect();
    const clientX = 'touches' in event ? event.touches[0].clientX : event.clientX;
    const clientY = 'touches' in event ? event.touches[0].clientY : event.clientY;

    dragStartX = clientX - rect.left;
    dragStartY = clientY - rect.top;
    dragHandle = 'move';
    isDragging = true;
    initialRect = { ...selectionRect };
  }
  
  function handleMouseDown(event: MouseEvent | TouchEvent) {
    if (!imageElement || isDragging) return;

    event.preventDefault();
    const rect = imageElement.getBoundingClientRect();
    const clientX = 'touches' in event ? event.touches[0].clientX : event.clientX;
    const clientY = 'touches' in event ? event.touches[0].clientY : event.clientY;

    startX = Math.max(0, Math.min(clientX - rect.left, rect.width));
    startY = Math.max(0, Math.min(clientY - rect.top, rect.height));
    currentX = startX;
    currentY = startY;
    isDrawing = true;

    // Reset selection when starting new draw
    selectionRect = { x: 0, y: 0, width: 0, height: 0 };
  }
  
  function handleMouseMove(event: MouseEvent) {
    if ((!isDrawing && !isDragging) || !imageElement) return;

    event.preventDefault();
    const rect = imageElement.getBoundingClientRect();
    const clientX = event.clientX;
    const clientY = event.clientY;
    const mouseX = Math.max(0, Math.min(clientX - rect.left, rect.width));
    const mouseY = Math.max(0, Math.min(clientY - rect.top, rect.height));

    if (isDrawing) {
      // Regular drawing mode
      currentX = mouseX;
      currentY = mouseY;

      // Update selection rect for drawing
      selectionRect = {
        x: Math.min(startX, currentX),
        y: Math.min(startY, currentY),
        width: Math.abs(currentX - startX),
        height: Math.abs(currentY - startY)
      };
    } else if (isDragging) {
      // Handle dragging/resizing
      const deltaX = mouseX - dragStartX;
      const deltaY = mouseY - dragStartY;

      if (dragHandle === 'move') {
        // Move the entire box
        selectionRect = {
          x: Math.max(0, Math.min(initialRect.x + deltaX, rect.width - initialRect.width)),
          y: Math.max(0, Math.min(initialRect.y + deltaY, rect.height - initialRect.height)),
          width: initialRect.width,
          height: initialRect.height
        };
      } else {
        // Resize based on handle
        updateSelectionForHandle(deltaX, deltaY, rect);
      }
    }
  }

  function updateSelectionForHandle(deltaX: number, deltaY: number, imageRect: DOMRect) {
    const minSize = 10;
    let newRect = { ...initialRect };

    switch (dragHandle) {
      case 'nw': // Top-left
        newRect.x = Math.max(0, Math.min(initialRect.x + deltaX, initialRect.x + initialRect.width - minSize));
        newRect.y = Math.max(0, Math.min(initialRect.y + deltaY, initialRect.y + initialRect.height - minSize));
        newRect.width = initialRect.width - (newRect.x - initialRect.x);
        newRect.height = initialRect.height - (newRect.y - initialRect.y);
        break;
      case 'ne': // Top-right
        newRect.y = Math.max(0, Math.min(initialRect.y + deltaY, initialRect.y + initialRect.height - minSize));
        newRect.width = Math.max(minSize, Math.min(initialRect.width + deltaX, imageRect.width - initialRect.x));
        newRect.height = initialRect.height - (newRect.y - initialRect.y);
        break;
      case 'sw': // Bottom-left
        newRect.x = Math.max(0, Math.min(initialRect.x + deltaX, initialRect.x + initialRect.width - minSize));
        newRect.width = initialRect.width - (newRect.x - initialRect.x);
        newRect.height = Math.max(minSize, Math.min(initialRect.height + deltaY, imageRect.height - initialRect.y));
        break;
      case 'se': // Bottom-right
        newRect.width = Math.max(minSize, Math.min(initialRect.width + deltaX, imageRect.width - initialRect.x));
        newRect.height = Math.max(minSize, Math.min(initialRect.height + deltaY, imageRect.height - initialRect.y));
        break;
      case 'n': // Top edge
        newRect.y = Math.max(0, Math.min(initialRect.y + deltaY, initialRect.y + initialRect.height - minSize));
        newRect.height = initialRect.height - (newRect.y - initialRect.y);
        break;
      case 's': // Bottom edge
        newRect.height = Math.max(minSize, Math.min(initialRect.height + deltaY, imageRect.height - initialRect.y));
        break;
      case 'w': // Left edge
        newRect.x = Math.max(0, Math.min(initialRect.x + deltaX, initialRect.x + initialRect.width - minSize));
        newRect.width = initialRect.width - (newRect.x - initialRect.x);
        break;
      case 'e': // Right edge
        newRect.width = Math.max(minSize, Math.min(initialRect.width + deltaX, imageRect.width - initialRect.x));
        break;
    }

    selectionRect = newRect;
  }
  
  function handleMouseUp() {
    if (isDrawing) {
      isDrawing = false;
      // Auto-save after drawing
      scheduleAutoSave();
    }
    if (isDragging) {
      isDragging = false;
      dragHandle = '';
      // Auto-save after dragging/resizing
      scheduleAutoSave();
    }
  }

  function scheduleAutoSave() {
    // Clear any existing timeout
    if (autoSaveTimeout) {
      clearTimeout(autoSaveTimeout);
    }

    // Schedule auto-save after 500ms of inactivity
    autoSaveTimeout = setTimeout(() => {
      if (selectionRect.width >= 10 && selectionRect.height >= 10) {
        saveSelection(true); // Silent auto-save
      }
    }, 500);
  }

  async function saveSelection(silent = false) {
    if (!selectedScreenshot || !$user || !imageElement) return;

    // Get the current session to access the token
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.access_token) return;

    // Use current selection rectangle
    const { x, y, width, height } = selectionRect;

    // Ensure minimum selection size
    if (width < 10 || height < 10) {
      if (!silent) {
        error = 'Selection area is too small. Please select a larger area.';
      }
      return;
    }

    // Convert pixel coordinates to percentages for responsive storage
    const imageRect = imageElement.getBoundingClientRect();
    const percentageCoords = pixelsToPercentage({ x, y, width, height }, imageRect);

    // Save both pixel and percentage coordinates
    try {
      const response = await fetch(`${API_BASE_URL}/screenshots/${selectedScreenshot.id}/coordinates`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          x, y, width, height,
          percentageX: percentageCoords.x,
          percentageY: percentageCoords.y,
          percentageWidth: percentageCoords.width,
          percentageHeight: percentageCoords.height
        })
      });

      if (response.ok) {
        await loadScreenshots();
        // Update the selected screenshot with new coordinates
        if (selectedScreenshot) {
          selectedScreenshot.coordinates = { x, y, width, height };
          selectedScreenshot.percentageCoordinates = percentageCoords;
        }
        if (!silent) {
          error = ''; // Clear any previous errors only for manual saves
        }
      } else {
        if (!silent) {
          error = 'Failed to save coordinates';
        }
      }
    } catch (err) {
      if (!silent) {
        error = 'Network error saving coordinates';
      }
      console.error('Error saving coordinates:', err);
    }
  }
  

  
  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
</script>

<div class="min-h-screen bg-dark text-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
      <div class="flex items-center space-x-3">
        <Monitor class="h-8 w-8 text-primary" />
        <h1 class="text-3xl font-bold">Website Tracking</h1>
      </div>
      
      <button
        class="flex items-center space-x-2 px-4 py-2 {isAtWebsiteTrackingLimit ? 'bg-gray-600 text-gray-300' : 'bg-primary text-white hover:bg-primary-dark'} rounded-lg transition-colors"
        on:click={handleAddWebsiteClick}
        disabled={loading}
      >
        {#if isAtWebsiteTrackingLimit}
          <Lock class="h-5 w-5" />
          <span>Limit Reached (4/4)</span>
        {:else}
          <Plus class="h-5 w-5" />
          <span>Add Website</span>
        {/if}
      </button>
    </div>
    
    <!-- Add Website Form -->
    {#if showAddForm}
      <div class="bg-dark-lighter rounded-lg p-6 mb-8 border border-gray-800">
        <h2 class="text-xl font-semibold mb-4">Capture Website Screenshot</h2>
        <div class="flex space-x-4">
          <input
            type="url"
            bind:value={newUrl}
            placeholder="Enter website URL (e.g., https://example.com)"
            class="flex-1 px-4 py-2 bg-dark border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary"
            disabled={loading}
          />
          <button
            class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors disabled:opacity-50"
            on:click={captureScreenshot}
            disabled={loading || !newUrl.trim()}
          >
            {#if loading}
              <div class="flex items-center space-x-2">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Capturing...</span>
              </div>
            {:else}
              <div class="flex items-center space-x-2">
                <Camera class="h-4 w-4" />
                <span>Capture</span>
              </div>
            {/if}
          </button>
        </div>
      </div>
    {/if}
    
    <!-- Error Message -->
    {#if error}
      <div class="bg-red-900/50 border border-red-700 text-red-200 px-4 py-3 rounded-lg mb-6">
        {error}
      </div>
    {/if}
    
    <!-- Screenshots Grid -->
    {#if loading && screenshots.length === 0}
      <div class="flex items-center justify-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span class="ml-3 text-gray-400">Loading screenshots...</span>
      </div>
    {:else if screenshots.length === 0}
      <div class="text-center py-12">
        <Monitor class="h-16 w-16 text-gray-600 mx-auto mb-4" />
        <h3 class="text-xl font-semibold text-gray-400 mb-2">No websites tracked yet</h3>
        <p class="text-gray-500 mb-6">Start by adding a website to track changes</p>
        <button
          class="px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
          on:click={handleAddWebsiteClick}
        >
          Add Your First Website
        </button>
      </div>
    {:else}
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {#each screenshots as screenshot}
          <div class="bg-dark-lighter rounded-lg border border-gray-800 overflow-hidden">
            <!-- Screenshot Image -->
            <div class="aspect-video bg-gray-900 relative overflow-hidden">
              <img
                src={screenshot.screenshotUrl}
                alt="Screenshot of {screenshot.url}"
                class="w-full h-full object-cover"
                loading="lazy"
              />
            </div>
            
            <!-- Card Content -->
            <div class="p-4">
              <h3 class="font-semibold text-white mb-2 truncate" title={screenshot.url}>
                {screenshot.url}
              </h3>
              <p class="text-sm text-gray-400 mb-4">
                Captured {formatDate(screenshot.createdAt)}
              </p>
              
              <!-- Actions -->
              <div class="flex space-x-2">
                <button
                  class="flex-1 flex items-center justify-center space-x-2 px-3 py-2 bg-primary/20 text-primary rounded-lg hover:bg-primary/30 transition-colors"
                  on:click={() => openRectangleSelector(screenshot)}
                >
                  <Settings class="h-4 w-4" />
                  <span class="text-sm">
                    {screenshot.coordinates ? 'Edit Area' : 'Select Area'}
                  </span>
                </button>
                <button
                  class="px-3 py-2 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors"
                  on:click={() => window.open(screenshot.url, '_blank')}
                  title="Visit Website"
                >
                  <Eye class="h-4 w-4" />
                </button>
                <button
                  class="px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                  on:click={() => deleteScreenshot(screenshot.id)}
                  title="Remove Website"
                >
                  <Trash2 class="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        {/each}
      </div>
    {/if}
  </div>
</div>

<!-- Rectangle Selector Modal -->
{#if showRectangleSelector && selectedScreenshot}
  <div class="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4">
    <div class="bg-dark-lighter rounded-lg max-w-6xl w-full max-h-[95vh] overflow-auto">
      <div class="p-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-xl font-semibold">Select Area to Monitor</h2>
          <button
            class="text-gray-400 hover:text-white transition-colors"
            on:click={closeRectangleSelector}
          >
            <X class="h-6 w-6" />
          </button>
        </div>

        <p class="text-gray-400 mb-6">
          Click and drag to select the area you want to monitor for changes. The selected area will be highlighted with a blue border.
        </p>

        <div class="relative inline-block bg-gray-900 rounded-lg overflow-hidden">
          <!-- Image Container with Overlay -->
          <div class="relative">
            <img
              bind:this={imageElement}
              src={selectedScreenshot.screenshotUrl}
              alt="Screenshot"
              class="max-w-full h-auto block"
            />

            <!-- Overlay Container -->
            <div
              class="absolute inset-0 cursor-crosshair touch-none"
              on:mousedown={handleMouseDown}
              on:mousemove={handleMouseMove}
              on:mouseup={handleMouseUp}
              on:touchstart={(e) => handleMouseDown(e)}
              on:touchend={handleMouseUp}
              role="button"
              tabindex="0"
              aria-label="Select area to monitor"
            >
              <!-- Shaded overlay for unselected areas -->
              {#if selectionRect.width > 0 && selectionRect.height > 0 && imageElement}
                <!-- Top overlay -->
                <div
                  class="absolute bg-black/60 pointer-events-none"
                  style="left: 0; top: 0; width: 100%; height: {selectionRect.y}px;"
                ></div>

                <!-- Bottom overlay -->
                <div
                  class="absolute bg-black/60 pointer-events-none"
                  style="left: 0; top: {selectionRect.y + selectionRect.height}px; width: 100%; height: calc(100% - {selectionRect.y + selectionRect.height}px);"
                ></div>

                <!-- Left overlay -->
                <div
                  class="absolute bg-black/60 pointer-events-none"
                  style="left: 0; top: {selectionRect.y}px; width: {selectionRect.x}px; height: {selectionRect.height}px;"
                ></div>

                <!-- Right overlay -->
                <div
                  class="absolute bg-black/60 pointer-events-none"
                  style="left: {selectionRect.x + selectionRect.width}px; top: {selectionRect.y}px; width: calc(100% - {selectionRect.x + selectionRect.width}px); height: {selectionRect.height}px;"
                ></div>

                <!-- Selection rectangle with bold blue border -->
                <div
                  class="absolute border-4 border-blue-500 bg-blue-500/10 shadow-lg cursor-move"
                  style="left: {selectionRect.x}px; top: {selectionRect.y}px; width: {selectionRect.width}px; height: {selectionRect.height}px;"
                  on:mousedown={handleBoxMouseDown}
                  on:touchstart={(e) => handleBoxMouseDown(e)}
                  role="button"
                  tabindex="0"
                  aria-label="Drag to move selection"
                >
                  <!-- Corner handles -->
                  <div
                    class="absolute -top-2 -left-2 w-4 h-4 bg-blue-500 border-2 border-white rounded-full cursor-nw-resize hover:bg-blue-600 transition-colors"
                    on:mousedown={(e) => handleHandleMouseDown(e, 'nw')}
                    role="button"
                    tabindex="0"
                    aria-label="Resize from top-left corner"
                  ></div>
                  <div
                    class="absolute -top-2 -right-2 w-4 h-4 bg-blue-500 border-2 border-white rounded-full cursor-ne-resize hover:bg-blue-600 transition-colors"
                    on:mousedown={(e) => handleHandleMouseDown(e, 'ne')}
                    role="button"
                    tabindex="0"
                    aria-label="Resize from top-right corner"
                  ></div>
                  <div
                    class="absolute -bottom-2 -left-2 w-4 h-4 bg-blue-500 border-2 border-white rounded-full cursor-sw-resize hover:bg-blue-600 transition-colors"
                    on:mousedown={(e) => handleHandleMouseDown(e, 'sw')}
                    role="button"
                    tabindex="0"
                    aria-label="Resize from bottom-left corner"
                  ></div>
                  <div
                    class="absolute -bottom-2 -right-2 w-4 h-4 bg-blue-500 border-2 border-white rounded-full cursor-se-resize hover:bg-blue-600 transition-colors"
                    on:mousedown={(e) => handleHandleMouseDown(e, 'se')}
                    role="button"
                    tabindex="0"
                    aria-label="Resize from bottom-right corner"
                  ></div>

                  <!-- Edge handles -->
                  <div
                    class="absolute -top-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-blue-500 border-2 border-white rounded-full cursor-n-resize hover:bg-blue-600 transition-colors"
                    on:mousedown={(e) => handleHandleMouseDown(e, 'n')}
                    role="button"
                    tabindex="0"
                    aria-label="Resize from top edge"
                  ></div>
                  <div
                    class="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-blue-500 border-2 border-white rounded-full cursor-s-resize hover:bg-blue-600 transition-colors"
                    on:mousedown={(e) => handleHandleMouseDown(e, 's')}
                    role="button"
                    tabindex="0"
                    aria-label="Resize from bottom edge"
                  ></div>
                  <div
                    class="absolute -left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 bg-blue-500 border-2 border-white rounded-full cursor-w-resize hover:bg-blue-600 transition-colors"
                    on:mousedown={(e) => handleHandleMouseDown(e, 'w')}
                    role="button"
                    tabindex="0"
                    aria-label="Resize from left edge"
                  ></div>
                  <div
                    class="absolute -right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 bg-blue-500 border-2 border-white rounded-full cursor-e-resize hover:bg-blue-600 transition-colors"
                    on:mousedown={(e) => handleHandleMouseDown(e, 'e')}
                    role="button"
                    tabindex="0"
                    aria-label="Resize from right edge"
                  ></div>
                </div>
              {/if}
            </div>
          </div>
        </div>

        <!-- Status and Close button -->
        <div class="flex justify-between items-center mt-6">
          <div class="text-sm text-gray-400">
            {#if selectionRect.width > 0 && selectionRect.height > 0}
              Selection: {Math.round(selectionRect.width)} × {Math.round(selectionRect.height)} pixels
              {#if selectionRect.width >= 10 && selectionRect.height >= 10}
                <span class="text-green-400 ml-2">• Auto-saving changes</span>
              {:else}
                <span class="text-yellow-400 ml-2">• Selection too small (minimum 10×10)</span>
              {/if}
            {:else}
              Click and drag to select an area to monitor
            {/if}
          </div>
          <button
            class="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium"
            on:click={closeRectangleSelector}
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
{/if}

<WebsiteTrackingLimitModal
  show={showWebsiteTrackingLimitModal}
  on:close={() => showWebsiteTrackingLimitModal = false}
/>
