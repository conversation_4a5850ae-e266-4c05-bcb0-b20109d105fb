package com.maplecan.scraper.controller;

import com.maplecan.scraper.configuration.UserPrincipal;
import com.maplecan.scraper.model.UserMetadata;
import com.maplecan.scraper.service.UnsubscribeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/account")
@Slf4j
public class AccountController {
    
    @Autowired
    private UnsubscribeService unsubscribeService;
    
    /**
     * Get user account information
     */
    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> getUserInfo(Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            String email = userPrincipal.getEmail();
            log.info("Getting account info for user: {}", email);

            Optional<UserMetadata> userOpt = unsubscribeService.getUserMetadata(email);
            
            if (userOpt.isPresent()) {
                log.info("Found existing user metadata for: {}", email);
                UserMetadata user = userOpt.get();
                Map<String, Object> response = new HashMap<>();
                response.put("email", user.getEmail());
                response.put("firstName", user.getFirstName() != null ? user.getFirstName() : "");
                response.put("lastName", user.getLastName() != null ? user.getLastName() : "");
                response.put("emailSubscription", user.getEmailSubscription() != null ? user.getEmailSubscription() : true);
                response.put("createdAt", user.getCreatedAt());
                response.put("updatedAt", user.getUpdatedAt());
                return ResponseEntity.ok(response);
            } else {
                log.info("No user metadata found for: {}, creating new user", email);
                // Create new user with default settings (emailSubscription = true)
                boolean created = unsubscribeService.updateUserInfo(email, null, null);
                log.info("User creation attempt result: {}", created);
                if (created) {
                    // Fetch the newly created user
                    Optional<UserMetadata> newUserOpt = unsubscribeService.getUserMetadata(email);
                    if (newUserOpt.isPresent()) {
                        log.info("Successfully created and retrieved new user: {}", email);
                        UserMetadata user = newUserOpt.get();
                        Map<String, Object> response = new HashMap<>();
                        response.put("email", user.getEmail());
                        response.put("firstName", user.getFirstName() != null ? user.getFirstName() : "");
                        response.put("lastName", user.getLastName() != null ? user.getLastName() : "");
                        response.put("emailSubscription", user.getEmailSubscription() != null ? user.getEmailSubscription() : true);
                        response.put("createdAt", user.getCreatedAt());
                        response.put("updatedAt", user.getUpdatedAt());
                        return ResponseEntity.ok(response);
                    } else {
                        log.warn("User creation succeeded but could not retrieve user: {}", email);
                    }
                } else {
                    log.error("Failed to create user: {}", email);
                }

                // Fallback: return default values if creation failed
                Map<String, Object> response = new HashMap<>();
                response.put("email", email);
                response.put("firstName", "");
                response.put("lastName", "");
                response.put("emailSubscription", true);
                response.put("createdAt", null);
                response.put("updatedAt", null);
                return ResponseEntity.ok(response);
            }
        } catch (Exception e) {
            log.error("Error getting user info", e);
            return ResponseEntity.internalServerError().body(Map.of(
                "error", "Failed to get user information"
            ));
        }
    }
    
    /**
     * Update user personal information
     */
    @PostMapping("/update-info")
    public ResponseEntity<Map<String, Object>> updateUserInfo(
            @RequestBody Map<String, String> request,
            Authentication authentication) {
        
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            String email = userPrincipal.getEmail();
            log.info("Updating personal info for user: {}", email);

            String firstName = request.get("firstName");
            String lastName = request.get("lastName");
            log.info("Update request - firstName: '{}', lastName: '{}'", firstName, lastName);
            
            // Validate input
            if (firstName != null && firstName.length() > 50) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "First name must be 50 characters or less"
                ));
            }
            
            if (lastName != null && lastName.length() > 50) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Last name must be 50 characters or less"
                ));
            }
            
            boolean success = unsubscribeService.updateUserInfo(email, firstName, lastName);
            
            if (success) {
                return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "Personal information updated successfully"
                ));
            } else {
                return ResponseEntity.internalServerError().body(Map.of(
                    "success", false,
                    "message", "Failed to update personal information"
                ));
            }
            
        } catch (Exception e) {
            log.error("Error updating user info", e);
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "An error occurred while updating your information"
            ));
        }
    }
    
    /**
     * Update email subscription preference
     */
    @PostMapping("/update-subscription")
    public ResponseEntity<Map<String, Object>> updateEmailSubscription(
            @RequestBody Map<String, Boolean> request,
            Authentication authentication) {
        
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            String email = userPrincipal.getEmail();
            log.info("Updating email subscription for user: {}", email);

            Boolean emailSubscription = request.get("emailSubscription");
            log.info("Email subscription update request: {}", emailSubscription);
            if (emailSubscription == null) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Email subscription preference is required"
                ));
            }
            
            boolean success = unsubscribeService.updateEmailSubscription(email, emailSubscription);
            
            if (success) {
                String message = emailSubscription ? 
                    "Email notifications enabled successfully" : 
                    "Email notifications disabled successfully";
                    
                return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", message,
                    "emailSubscription", emailSubscription
                ));
            } else {
                return ResponseEntity.internalServerError().body(Map.of(
                    "success", false,
                    "message", "Failed to update email subscription preference"
                ));
            }
            
        } catch (Exception e) {
            log.error("Error updating email subscription", e);
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "An error occurred while updating your subscription preference"
            ));
        }
    }
}
