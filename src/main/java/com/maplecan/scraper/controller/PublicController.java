package com.maplecan.scraper.controller;

import com.maplecan.scraper.dto.PostResponse;
import com.maplecan.scraper.model.Flyers;
import com.maplecan.scraper.model.ItemDetail;
import com.maplecan.scraper.model.PriceChange;
import com.maplecan.scraper.model.ReceiptItem;
import com.maplecan.scraper.model.ScrapedData;
import com.maplecan.scraper.repository.PriceChangeRepository;
import com.maplecan.scraper.service.CommunityService;
import com.maplecan.scraper.service.CostcoCanadaCouponScraper;
import com.maplecan.scraper.service.CostcoFlyerService;
import com.maplecan.scraper.service.GoogleVisionService;
import com.maplecan.scraper.service.ImageComparisonService;
import com.maplecan.scraper.service.ScrapingBeeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@RestController
@RequestMapping("/api/public")
public class PublicController {

    @Autowired
    private ScrapingBeeService scrapingBeeService;

    @Autowired
    private CostcoCanadaCouponScraper costcoCanadaCouponScraper;

    @Autowired
    private CostcoFlyerService costcoFlyerService;

    @Autowired
    private PriceChangeRepository priceChangeRepository;

    @Autowired
    private GoogleVisionService googleVisionService;

    @Autowired
    private CommunityService communityService;

    @Autowired
    private ImageComparisonService imageComparisonService;

    @GetMapping("/vision/analyze")
    public ResponseEntity<?> analyzeImage(@RequestParam String imageUrl) {
        try {
            Map<String, Object> receiptData = googleVisionService.processReceipt(imageUrl);
            return ResponseEntity.ok(receiptData);
        } catch (IOException e) {
            return ResponseEntity.status(500).body("Error processing receipt: " + e.getMessage());
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @GetMapping("/fetch-url")
    public ScrapedData fetchUrlV2(@RequestParam String url, @RequestParam String province) {
        return scrapingBeeService.fetch(url, province);
    }

    @GetMapping("/fetch-item")
    public ScrapedData fetchItemNumber(@RequestParam String itemNumber, @RequestParam String storeAndCountryCode) {
        return scrapingBeeService.fetchByItemNumber(itemNumber, storeAndCountryCode);
    }

    @GetMapping("/flyers")
    public Flyers getFlyers() throws Exception {
        return costcoFlyerService.scrapeFlyer("B4E3B6");
    }

    @GetMapping("/flyers/latest")
    public ResponseEntity<Flyers> getLatestFlyers(@RequestParam String vendorName) throws Exception {
        return costcoFlyerService.getLatestFlyer(vendorName)
                .map(ResponseEntity::ok) // Return 200 OK with the flyer data
                .orElseGet(() -> ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(null));
    }


    @GetMapping("/flyers/screenshot")
    public void getFlyersScreenshot() throws Exception {
        costcoFlyerService.screenshotFlyer("B4E3B6");
    }

    @GetMapping("/price/change")
    public List<PriceChange> getPriceByUrlAndDates(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {

        // Default end date to current date if not provided
        if (endDate == null) {
            endDate = LocalDateTime.now();
        }

        // Default start date to 4 months before the end date if not provided
        if (startDate == null) {
            startDate = endDate.minusDays(7);
        }
        return priceChangeRepository.findByCreatedDateBetweenOrderByCreatedDateDesc(startDate, endDate);
    }

    // Public Community Endpoints (for API access)
    @GetMapping("/community/posts")
    public ResponseEntity<Page<PostResponse>> getPublicPosts(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String store,
            @RequestParam(required = false) String category,
            @RequestParam(defaultValue = "new") String sort) {
        try {
            Page<PostResponse> posts = communityService.getPosts(page, size, store, category, sort);
            return ResponseEntity.ok(posts);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/community/posts/{postId}")
    public ResponseEntity<?> getPublicPost(@PathVariable String postId) {
        try {
            PostResponse post = communityService.getPost(postId);
            return ResponseEntity.ok(post);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to get post"));
        }
    }
}