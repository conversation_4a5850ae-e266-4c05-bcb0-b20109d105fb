package com.maplecan.scraper.controller;

import com.maplecan.scraper.configuration.UserPrincipal;
import com.maplecan.scraper.model.AddNewProductRequest;
import com.maplecan.scraper.model.AddNewProductRequestByItemNumber;
import com.maplecan.scraper.model.ScrapedData;
import com.maplecan.scraper.model.ScrapedDataPatchRequest;
import com.maplecan.scraper.model.ScreenshotData;
import com.maplecan.scraper.model.ScreenshotResponse;
import com.maplecan.scraper.service.ScrapingBeeService;
import com.maplecan.scraper.service.ScreenshotService;
import com.maplecan.scraper.service.BaselineImageService;
import com.maplecan.scraper.repository.ScreenshotDataRepository;
import com.maplecan.scraper.job.ScreenshotComparisonJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.time.LocalDateTime;
import jakarta.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@RestController
public class ScraperController {

    private final ScrapingBeeService scrapingBeeService;
    private final ScreenshotService screenshotService;
    private final ScreenshotDataRepository screenshotDataRepository;
    private final BaselineImageService baselineImageService;
    private final ScreenshotComparisonJob screenshotComparisonJob;

    @Value("${supabase.service.key:}")
    private String supabaseServiceKey;

    public ScraperController(ScrapingBeeService scrapingBeeService,
                           ScreenshotService screenshotService,
                           ScreenshotDataRepository screenshotDataRepository,
                           BaselineImageService baselineImageService,
                           ScreenshotComparisonJob screenshotComparisonJob) {
        this.scrapingBeeService = scrapingBeeService;
        this.screenshotService = screenshotService;
        this.screenshotDataRepository = screenshotDataRepository;
        this.baselineImageService = baselineImageService;
        this.screenshotComparisonJob = screenshotComparisonJob;
    }

    @GetMapping("/fetch-url")
    public ScrapedData fetchUrl(@RequestParam String url, @RequestParam String province) {
        return scrapingBeeService.fetchUrlAndSave(url, province);
    }

    @GetMapping("/v2/fetch-url")
    public ScrapedData fetchUrlV2(@RequestParam String url, @RequestParam String province) {
        return scrapingBeeService.fetch(url, province);
    }

    @PostMapping("/fetch-url")
    public ResponseEntity<Object> fetchUrl(@RequestBody AddNewProductRequest request) {
        UserPrincipal userPrincipal = (UserPrincipal) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        String email2 = userPrincipal.getEmail();
        log.info("Getting product list for {}", email2);
        request.setEmail(email2);
        List<ScrapedData> scrapedDataList = scrapingBeeService.getProductList(request.getEmail());
        if (scrapedDataList.size() > 4) {
            return ResponseEntity.badRequest().body("Cannot exceed 5 products");
        }
        return ResponseEntity.ok(scrapingBeeService.fetchUrlAndSave(request));
    }

    @PostMapping("/fetchByItem")
    public ResponseEntity<Object> fetchItemNumber(@RequestBody AddNewProductRequestByItemNumber request) {
        UserPrincipal userPrincipal = (UserPrincipal) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        String email2 = userPrincipal.getEmail();
        log.info("Getting product list for {}", email2);
        request.setEmail(email2);
        List<ScrapedData> scrapedDataList = scrapingBeeService.getProductList(request.getEmail());
        if (scrapedDataList.size() > 4) {
            return ResponseEntity.badRequest().body("Cannot exceed 5 products");
        }
        return ResponseEntity.ok(scrapingBeeService.fetchItemNumberAndSave(request));
    }

    @DeleteMapping("/products/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteById(@PathVariable String id) {
        scrapingBeeService.deleteById(id);
    }

    @GetMapping("/productList")
    public List<ScrapedData> fetchList(@RequestParam String email) {
        UserPrincipal userPrincipal = (UserPrincipal) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        String email2 = userPrincipal.getEmail();
        log.info("Getting product list for {}", email2);

        return scrapingBeeService.getProductList(email2);
    }

    @PatchMapping("/products/{id}")
    public ResponseEntity<String> updateScrapedData(@PathVariable String id,
                                                    @RequestBody ScrapedDataPatchRequest patchRequest) {
        ScrapedData existingData = scrapingBeeService.findById(id).orElse(null);

        if (existingData == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Scraped data with ID " + id + " not found.");
        }

        // Update fields based on the patch request
        if (patchRequest.getUserPrice() != null) {
            existingData.setUserPrice(patchRequest.getUserPrice());
        }

        if (patchRequest.getProvince() != null) {
            existingData.setProvince(patchRequest.getProvince());
        }

        existingData.setUpdatedTimestamp(LocalDateTime.now());
        scrapingBeeService.save(existingData);
        return ResponseEntity.ok("Scraped data updated successfully.");
    }

    // Screenshot endpoints
    @PostMapping("/screenshots")
    public ResponseEntity<ScreenshotResponse> captureScreenshot(@RequestParam String url, @RequestParam String supabaseId, HttpServletRequest request) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            String userId = userPrincipal.getSupabaseUserId();
            String userEmail = userPrincipal.getEmail();

            // Extract JWT token from Authorization header
            String authHeader = request.getHeader("Authorization");
            String jwtToken = null;
            if (authHeader != null && authHeader.startsWith("Bearer ")) {
                jwtToken = authHeader.substring(7);
            }

            ScreenshotData screenshotData = screenshotService.captureScreenshotWithMetadata(url, userId, userEmail, supabaseId, jwtToken);

            ScreenshotResponse response = ScreenshotResponse.builder()
                    .id(screenshotData.getId())
                    .screenshotUrl(screenshotData.getScreenshotUrl())
                    .originalUrl(screenshotData.getUrl())
                    .capturedAt(screenshotData.getCreatedAt())
                    .status("success")
                    .message("Screenshot captured successfully")
                    .build();

            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            ScreenshotResponse response = ScreenshotResponse.builder()
                    .originalUrl(url)
                    .capturedAt(LocalDateTime.now())
                    .status("error")
                    .message(e.getMessage())
                    .build();
            return ResponseEntity.badRequest().body(response);
        } catch (IOException e) {
            ScreenshotResponse response = ScreenshotResponse.builder()
                    .originalUrl(url)
                    .capturedAt(LocalDateTime.now())
                    .status("error")
                    .message(e.getMessage())
                    .build();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @GetMapping("/screenshots")
    public ResponseEntity<List<ScreenshotData>> getUserScreenshots() {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            String userId = userPrincipal.getSupabaseUserId();

            // Only return non-deleted screenshots
            List<ScreenshotData> screenshots = screenshotDataRepository.findByUserIdAndDeleted(userId, false);
            return ResponseEntity.ok(screenshots);
        } catch (Exception e) {
            log.error("Error getting user screenshots", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PatchMapping("/screenshots/{id}/coordinates")
    public ResponseEntity<ScreenshotData> updateScreenshotCoordinates(
            @PathVariable String id,
            @RequestBody Map<String, Double> coordinates,
            HttpServletRequest request) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            String userId = userPrincipal.getSupabaseUserId();

            Optional<ScreenshotData> optionalScreenshot = screenshotDataRepository.findById(id);
            if (optionalScreenshot.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            ScreenshotData screenshot = optionalScreenshot.get();

            // Verify ownership
            if (!userId.equals(screenshot.getUserId())) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            // Update coordinates (both pixel and percentage)
            ScreenshotData.Coordinates coords = ScreenshotData.Coordinates.builder()
                    .x(coordinates.get("x"))
                    .y(coordinates.get("y"))
                    .width(coordinates.get("width"))
                    .height(coordinates.get("height"))
                    .percentageX(coordinates.get("percentageX"))
                    .percentageY(coordinates.get("percentageY"))
                    .percentageWidth(coordinates.get("percentageWidth"))
                    .percentageHeight(coordinates.get("percentageHeight"))
                    .build();

            screenshot.setCoordinates(coords);
            screenshot.setUpdatedAt(LocalDateTime.now());

            // Save the updated screenshot first
            ScreenshotData updatedScreenshot = screenshotDataRepository.save(screenshot);

            // Generate and save baseline image from the selected area
            try {
                String jwtToken = request.getHeader("Authorization");
                if (jwtToken != null && jwtToken.startsWith("Bearer ")) {
                    jwtToken = jwtToken.substring(7);
                }

                String baselineImageUrl = baselineImageService.extractAndSaveBaselineImage(updatedScreenshot, jwtToken);
                updatedScreenshot.setBaselineImageUrl(baselineImageUrl);

                // Save again with baseline image URL
                updatedScreenshot = screenshotDataRepository.save(updatedScreenshot);

                log.info("Successfully generated baseline image for screenshot ID: {}", updatedScreenshot.getId());
            } catch (Exception e) {
                log.error("Failed to generate baseline image for screenshot ID: {}", updatedScreenshot.getId(), e);
                // Continue without failing the coordinate update
            }

            return ResponseEntity.ok(updatedScreenshot);
        } catch (Exception e) {
            log.error("Error updating screenshot coordinates", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @DeleteMapping("/screenshots/{id}")
    public ResponseEntity<Void> deleteScreenshot(@PathVariable String id) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            String userId = userPrincipal.getSupabaseUserId();

            Optional<ScreenshotData> optionalScreenshot = screenshotDataRepository.findById(id);
            if (optionalScreenshot.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            ScreenshotData screenshot = optionalScreenshot.get();

            // Verify ownership
            if (!userId.equals(screenshot.getUserId())) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            // Soft delete by setting deleted flag to true
            screenshot.setDeleted(true);
            screenshot.setUpdatedAt(LocalDateTime.now());
            screenshotDataRepository.save(screenshot);

            return ResponseEntity.noContent().build();
        } catch (Exception e) {
            log.error("Error deleting screenshot", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Endpoint for daily automated screenshot capture (for scheduled jobs)
     * This captures new screenshots for all active tracking records
     */
    @PostMapping("/screenshots/daily-capture")
    public ResponseEntity<Map<String, Object>> captureDailyScreenshots() {
        try {
            List<ScreenshotData> activeRecords = screenshotService.getActiveScreenshotTrackingRecords();
            List<String> successfulCaptures = new ArrayList<>();
            List<String> failedCaptures = new ArrayList<>();

            for (ScreenshotData record : activeRecords) {
                try {
                    // Use service key for automated captures (no user JWT needed)
                    String newScreenshotUrl = screenshotService.captureDailyScreenshot(record, supabaseServiceKey);
                    successfulCaptures.add(record.getId() + " (" + record.getUrl() + ")");

                    log.info("Daily screenshot captured for tracking ID: {} - URL: {}",
                            record.getId(), newScreenshotUrl);
                } catch (Exception e) {
                    failedCaptures.add(record.getId() + " (" + record.getUrl() + "): " + e.getMessage());
                    log.error("Failed to capture daily screenshot for tracking ID: {}", record.getId(), e);
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("totalRecords", activeRecords.size());
            result.put("successful", successfulCaptures.size());
            result.put("failed", failedCaptures.size());
            result.put("successfulCaptures", successfulCaptures);
            result.put("failedCaptures", failedCaptures);
            result.put("timestamp", LocalDateTime.now());

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error during daily screenshot capture", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "Daily capture failed: " + e.getMessage());
            errorResult.put("timestamp", LocalDateTime.now());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResult);
        }
    }

    /**
     * Endpoint to trigger screenshot comparison job manually (for testing)
     */
    @PostMapping("/screenshots/trigger-comparison-job")
    public ResponseEntity<String> triggerScreenshotComparisonJob() {
        try {
            screenshotComparisonJob.checkScreenshotChanges();
            return ResponseEntity.ok("Screenshot comparison job completed successfully");
        } catch (Exception e) {
            log.error("Error triggering screenshot comparison job", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to trigger screenshot comparison job: " + e.getMessage());
        }
    }

}
