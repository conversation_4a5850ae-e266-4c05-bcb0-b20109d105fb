package com.maplecan.scraper.controller;

import com.maplecan.scraper.configuration.UserPrincipal;
import com.maplecan.scraper.model.AddNewProductRequest;
import com.maplecan.scraper.model.AddNewProductRequestByItemNumber;
import com.maplecan.scraper.model.ScrapedData;
import com.maplecan.scraper.model.ScrapedDataPatchRequest;
import com.maplecan.scraper.model.ScreenshotData;
import com.maplecan.scraper.model.ScreenshotResponse;
import com.maplecan.scraper.service.ScrapingBeeService;
import com.maplecan.scraper.service.ScreenshotService;
import com.maplecan.scraper.repository.ScreenshotDataRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@RestController
public class ScraperController {

    private final ScrapingBeeService scrapingBeeService;
    private final ScreenshotService screenshotService;
    private final ScreenshotDataRepository screenshotDataRepository;

    public ScraperController(ScrapingBeeService scrapingBeeService,
                           ScreenshotService screenshotService,
                           ScreenshotDataRepository screenshotDataRepository) {
        this.scrapingBeeService = scrapingBeeService;
        this.screenshotService = screenshotService;
        this.screenshotDataRepository = screenshotDataRepository;
    }

    @GetMapping("/fetch-url")
    public ScrapedData fetchUrl(@RequestParam String url, @RequestParam String province) {
        return scrapingBeeService.fetchUrlAndSave(url, province);
    }

    @GetMapping("/v2/fetch-url")
    public ScrapedData fetchUrlV2(@RequestParam String url, @RequestParam String province) {
        return scrapingBeeService.fetch(url, province);
    }

    @PostMapping("/fetch-url")
    public ResponseEntity<Object> fetchUrl(@RequestBody AddNewProductRequest request) {
        UserPrincipal userPrincipal = (UserPrincipal) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        String email2 = userPrincipal.getEmail();
        log.info("Getting product list for {}", email2);
        request.setEmail(email2);
        List<ScrapedData> scrapedDataList = scrapingBeeService.getProductList(request.getEmail());
        if (scrapedDataList.size() > 4) {
            return ResponseEntity.badRequest().body("Cannot exceed 5 products");
        }
        return ResponseEntity.ok(scrapingBeeService.fetchUrlAndSave(request));
    }

    @PostMapping("/fetchByItem")
    public ResponseEntity<Object> fetchItemNumber(@RequestBody AddNewProductRequestByItemNumber request) {
        UserPrincipal userPrincipal = (UserPrincipal) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        String email2 = userPrincipal.getEmail();
        log.info("Getting product list for {}", email2);
        request.setEmail(email2);
        List<ScrapedData> scrapedDataList = scrapingBeeService.getProductList(request.getEmail());
        if (scrapedDataList.size() > 4) {
            return ResponseEntity.badRequest().body("Cannot exceed 5 products");
        }
        return ResponseEntity.ok(scrapingBeeService.fetchItemNumberAndSave(request));
    }

    @DeleteMapping("/products/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteById(@PathVariable String id) {
        scrapingBeeService.deleteById(id);
    }

    @GetMapping("/productList")
    public List<ScrapedData> fetchList(@RequestParam String email) {
        UserPrincipal userPrincipal = (UserPrincipal) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        String email2 = userPrincipal.getEmail();
        log.info("Getting product list for {}", email2);

        return scrapingBeeService.getProductList(email2);
    }

    @PatchMapping("/products/{id}")
    public ResponseEntity<String> updateScrapedData(@PathVariable String id,
                                                    @RequestBody ScrapedDataPatchRequest patchRequest) {
        ScrapedData existingData = scrapingBeeService.findById(id).orElse(null);

        if (existingData == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Scraped data with ID " + id + " not found.");
        }

        // Update fields based on the patch request
        if (patchRequest.getUserPrice() != null) {
            existingData.setUserPrice(patchRequest.getUserPrice());
        }

        if (patchRequest.getProvince() != null) {
            existingData.setProvince(patchRequest.getProvince());
        }

        existingData.setUpdatedTimestamp(LocalDateTime.now());
        scrapingBeeService.save(existingData);
        return ResponseEntity.ok("Scraped data updated successfully.");
    }

    // Screenshot endpoints
    @PostMapping("/screenshots")
    public ResponseEntity<ScreenshotResponse> captureScreenshot(@RequestParam String url) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            String userId = userPrincipal.getSupabaseUserId();
            String userEmail = userPrincipal.getEmail();

            ScreenshotData screenshotData = screenshotService.captureScreenshotWithMetadata(url, userId, userEmail);

            ScreenshotResponse response = ScreenshotResponse.builder()
                    .id(screenshotData.getId())
                    .screenshotUrl(screenshotData.getScreenshotUrl())
                    .originalUrl(screenshotData.getUrl())
                    .capturedAt(screenshotData.getCreatedAt())
                    .status("success")
                    .message("Screenshot captured successfully")
                    .build();

            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            ScreenshotResponse response = ScreenshotResponse.builder()
                    .originalUrl(url)
                    .capturedAt(LocalDateTime.now())
                    .status("error")
                    .message(e.getMessage())
                    .build();
            return ResponseEntity.badRequest().body(response);
        } catch (IOException e) {
            ScreenshotResponse response = ScreenshotResponse.builder()
                    .originalUrl(url)
                    .capturedAt(LocalDateTime.now())
                    .status("error")
                    .message("Failed to capture screenshot: " + e.getMessage())
                    .build();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @GetMapping("/screenshots")
    public ResponseEntity<List<ScreenshotData>> getUserScreenshots() {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            String userId = userPrincipal.getSupabaseUserId();

            List<ScreenshotData> screenshots = screenshotDataRepository.findByUserId(userId);
            return ResponseEntity.ok(screenshots);
        } catch (Exception e) {
            log.error("Error getting user screenshots", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PatchMapping("/screenshots/{id}/coordinates")
    public ResponseEntity<ScreenshotData> updateScreenshotCoordinates(
            @PathVariable String id,
            @RequestBody Map<String, Double> coordinates) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            String userId = userPrincipal.getSupabaseUserId();

            Optional<ScreenshotData> optionalScreenshot = screenshotDataRepository.findById(id);
            if (optionalScreenshot.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            ScreenshotData screenshot = optionalScreenshot.get();

            // Verify ownership
            if (!userId.equals(screenshot.getUserId())) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            // Update coordinates
            ScreenshotData.Coordinates coords = ScreenshotData.Coordinates.builder()
                    .x(coordinates.get("x"))
                    .y(coordinates.get("y"))
                    .width(coordinates.get("width"))
                    .height(coordinates.get("height"))
                    .build();

            screenshot.setCoordinates(coords);
            screenshot.setUpdatedAt(LocalDateTime.now());

            ScreenshotData updatedScreenshot = screenshotDataRepository.save(screenshot);
            return ResponseEntity.ok(updatedScreenshot);
        } catch (Exception e) {
            log.error("Error updating screenshot coordinates", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

}
