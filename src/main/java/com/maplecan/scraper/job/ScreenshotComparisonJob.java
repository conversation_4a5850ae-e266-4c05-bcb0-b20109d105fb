package com.maplecan.scraper.job;

import com.maplecan.scraper.model.ImageComparisonResult;
import com.maplecan.scraper.model.ScreenshotData;
import com.maplecan.scraper.repository.ScreenshotDataRepository;
import com.maplecan.scraper.service.BaselineImageService;
import com.maplecan.scraper.service.ImageComparisonService;
import com.maplecan.scraper.service.ScreenshotService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class ScreenshotComparisonJob {

    private final ScreenshotDataRepository screenshotDataRepository;
    private final ScreenshotService screenshotService;
    private final BaselineImageService baselineImageService;
    private final ImageComparisonService imageComparisonService;

    @Value("${supabase.service.key:}")
    private String supabaseServiceKey;

    /**
     * Daily job to check for changes in tracked website screenshots
     * Runs every day at 2:00 AM UTC
     */
    @Scheduled(cron = "0 0 2 * * *")
    public void checkScreenshotChanges() {
        log.info("Starting daily screenshot comparison job...");
        
        try {
            // Get all active screenshot tracking records (not deleted and have coordinates)
            List<ScreenshotData> activeScreenshots = screenshotDataRepository.findByDeletedFalseAndCoordinatesIsNotNull();
            
            log.info("Found {} active screenshot tracking records to process", activeScreenshots.size());
            
            for (ScreenshotData screenshot : activeScreenshots) {
                try {
                    processScreenshotComparison(screenshot);
                } catch (Exception e) {
                    log.error("Failed to process screenshot comparison for ID: {}", screenshot.getId(), e);
                    // Continue with next screenshot even if one fails
                }
            }
            
            log.info("Daily screenshot comparison job completed successfully");
        } catch (Exception e) {
            log.error("Error during daily screenshot comparison job", e);
        }
    }

    /**
     * Process individual screenshot comparison
     */
    private void processScreenshotComparison(ScreenshotData screenshotData) {
        log.info("Processing screenshot comparison for ID: {}, URL: {}", 
                screenshotData.getId(), screenshotData.getUrl());
        
        try {
            // Skip if no baseline image exists
            if (screenshotData.getBaselineImageUrl() == null || screenshotData.getBaselineImageUrl().isEmpty()) {
                log.warn("No baseline image found for screenshot ID: {}, skipping comparison", screenshotData.getId());
                return;
            }
            
            // Capture new screenshot
            String newScreenshotUrl = captureNewScreenshot(screenshotData);
            if (newScreenshotUrl == null) {
                log.error("Failed to capture new screenshot for ID: {}", screenshotData.getId());
                return;
            }
            
            // Extract comparison image from the same area as baseline
            String comparisonImageUrl = baselineImageService.extractAndSaveComparisonImage(
                newScreenshotUrl, 
                screenshotData.getCoordinates(),
                screenshotData.getUserId(),
                screenshotData.getSupabaseId(),
                supabaseServiceKey
            );
            
            // Compare baseline with new cropped image
            ImageComparisonResult comparisonResult = imageComparisonService.compareImages(
                screenshotData.getBaselineImageUrl(),
                comparisonImageUrl
            );
            
            // Create change detection record
            ScreenshotData.ChangeDetection changeDetection = ScreenshotData.ChangeDetection.builder()
                .detectedAt(LocalDateTime.now())
                .newScreenshotUrl(newScreenshotUrl)
                .comparisonImageUrl(comparisonImageUrl)
                .changeDetected(comparisonResult.isHasDifferences())
                .changeDescription(generateChangeDescription(comparisonResult))
                .build();
            
            // Add to change history
            screenshotData.getChangeHistory().add(changeDetection);
            screenshotData.setLastComparisonCheck(LocalDateTime.now());
            
            // Save updated screenshot data
            screenshotDataRepository.save(screenshotData);
            
            // Log results
            if (comparisonResult.isHasDifferences()) {
                System.out.println("=== CHANGE DETECTED ===");
                System.out.println("Screenshot ID: " + screenshotData.getId());
                System.out.println("URL: " + screenshotData.getUrl());
                System.out.println("User ID: " + screenshotData.getUserId());
                System.out.println("Overall Similarity: " + String.format("%.2f%%", comparisonResult.getOverallSimilarity() * 100));
                System.out.println("Baseline Image: " + screenshotData.getBaselineImageUrl());
                System.out.println("New Screenshot: " + newScreenshotUrl);
                System.out.println("Comparison Image: " + comparisonImageUrl);
                System.out.println("Change Description: " + changeDetection.getChangeDescription());
                System.out.println("Detected At: " + changeDetection.getDetectedAt());
                System.out.println("========================");
                
                log.info("Change detected for screenshot ID: {} with similarity: {}", 
                        screenshotData.getId(), comparisonResult.getOverallSimilarity());
            } else {
                log.debug("No significant changes detected for screenshot ID: {}", screenshotData.getId());
            }
            
        } catch (Exception e) {
            log.error("Error processing screenshot comparison for ID: {}", screenshotData.getId(), e);
            throw e;
        }
    }

    /**
     * Capture new screenshot for comparison
     */
    private String captureNewScreenshot(ScreenshotData screenshotData) {
        try {
            // Use the screenshot service to capture a new screenshot
            byte[] screenshotBytes = screenshotService.captureScreenshot(screenshotData.getUrl());
            
            if (screenshotBytes == null || screenshotBytes.length == 0) {
                log.error("Screenshot capture returned empty result for URL: {}", screenshotData.getUrl());
                return null;
            }
            
            // Upload new screenshot to Supabase with organized naming
            return screenshotService.uploadScreenshotWithDataId(
                screenshotBytes,
                screenshotData.getUserId(),
                screenshotData.getSupabaseId(),
                screenshotData.getUrl(),
                supabaseServiceKey
            );
            
        } catch (Exception e) {
            log.error("Failed to capture new screenshot for URL: {}", screenshotData.getUrl(), e);
            return null;
        }
    }

    /**
     * Generate human-readable change description based on comparison results
     */
    private String generateChangeDescription(ImageComparisonResult result) {
        if (!result.isHasDifferences()) {
            return "No significant changes detected";
        }
        
        StringBuilder description = new StringBuilder();
        double similarity = result.getOverallSimilarity() * 100;
        
        if (result.isSignificantDifferences()) {
            description.append("Significant changes detected (").append(String.format("%.1f", similarity)).append("% similarity)");
        } else {
            description.append("Minor changes detected (").append(String.format("%.1f", similarity)).append("% similarity)");
        }
        
        // Add specific metrics if available
        if (result.getPixelSimilarity() < 0.9) {
            description.append(", pixel-level differences");
        }
        if (result.getStructuralSimilarity() < 0.9) {
            description.append(", structural changes");
        }
        if (result.getHistogramSimilarity() < 0.9) {
            description.append(", color distribution changes");
        }
        
        return description.toString();
    }
}
