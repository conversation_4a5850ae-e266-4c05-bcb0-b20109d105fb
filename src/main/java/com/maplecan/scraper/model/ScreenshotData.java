package com.maplecan.scraper.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "screenshot_data")
public class ScreenshotData {
    
    @Id
    private String id;

    private String supabaseId;

    private String url;
    
    private String screenshotUrl;
    
    private Coordinates coordinates;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
    
    private String userId;

    private String userEmail;

    @Builder.Default
    private Boolean deleted = false;

    // Baseline image URL for comparison (cropped area from original screenshot)
    private String baselineImageUrl;

    // Change detection history
    @Builder.Default
    private List<ChangeDetection> changeHistory = new ArrayList<>();

    // Last comparison check timestamp
    private LocalDateTime lastComparisonCheck;

    // History of all scraped images with date as key and URL as value
    @Builder.Default
    private Map<String, String> scrapedImages = new HashMap<>();
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Coordinates {
        private double x;
        private double y;
        private double width;
        private double height;

        // Percentage-based coordinates for responsive display
        private Double percentageX;
        private Double percentageY;
        private Double percentageWidth;
        private Double percentageHeight;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChangeDetection {
        private LocalDateTime detectedAt;
        private String newScreenshotUrl;
        private String comparisonImageUrl; // URL to the new cropped image that was compared
        private boolean changeDetected;
        private String changeDescription; // Optional description of what changed
    }
}
