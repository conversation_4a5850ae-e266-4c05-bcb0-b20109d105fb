package com.maplecan.scraper.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "screenshot_data")
public class ScreenshotData {
    
    @Id
    private String id;

    private String supabaseId;

    private String url;
    
    private String screenshotUrl;
    
    private Coordinates coordinates;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
    
    private String userId;

    private String userEmail;

    @Builder.Default
    private Boolean deleted = false;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Coordinates {
        private double x;
        private double y;
        private double width;
        private double height;

        // Percentage-based coordinates for responsive display
        private Double percentageX;
        private Double percentageY;
        private Double percentageWidth;
        private Double percentageHeight;
    }
}
