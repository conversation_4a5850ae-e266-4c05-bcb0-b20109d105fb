package com.maplecan.scraper.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "user_metadata")
public class UserMetadata {

    @Id
    private String id;

    @Indexed(unique = true)
    private String email;

    private String firstName;
    private String lastName;

    @Builder.Default
    private Boolean emailSubscription = true;

    private LocalDateTime unsubscribedAt;
    private String unsubscribeReason;

    @Builder.Default
    private String country = "Canada";

    @Builder.Default
    private LocalDateTime createdAt = LocalDateTime.now();

    @Builder.Default
    private LocalDateTime updatedAt = LocalDateTime.now();

    // Helper methods
    public String getFullName() {
        if (firstName != null && lastName != null) {
            return firstName + " " + lastName;
        } else if (firstName != null) {
            return firstName;
        } else if (lastName != null) {
            return lastName;
        }
        return email; // Fallback to email
    }
}

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

@Document(collection = "user_metadata")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserMetadata {
    
    @Id
    private String id;
    
    @Indexed(unique = true)
    private String email;

    // Personal information
    private String firstName;
    private String lastName;

    @Builder.Default
    private Boolean emailSubscription = true; // Default to subscribed
    
    @Builder.Default
    private LocalDateTime createdAt = LocalDateTime.now();
    
    @Builder.Default
    private LocalDateTime updatedAt = LocalDateTime.now();
    
    // Track unsubscribe details for analytics
    private LocalDateTime unsubscribedAt;
    private String unsubscribeReason;
}
