package com.maplecan.scraper.repository;

import com.maplecan.scraper.model.ScreenshotData;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ScreenshotDataRepository extends MongoRepository<ScreenshotData, String> {
    
    /**
     * Find all screenshot data for a specific user
     * @param userId The user ID
     * @return List of screenshot data for the user
     */
    List<ScreenshotData> findByUserId(String userId);
    
    /**
     * Find all screenshot data for a specific user email
     * @param userEmail The user email
     * @return List of screenshot data for the user
     */
    List<ScreenshotData> findByUserEmail(String userEmail);
    
    /**
     * Find screenshot data by URL and user ID
     * @param url The URL
     * @param userId The user ID
     * @return List of screenshot data for the URL and user
     */
    List<ScreenshotData> findByUrlAndUserId(String url, String userId);
}
