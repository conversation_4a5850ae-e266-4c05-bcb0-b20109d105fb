package com.maplecan.scraper.service;

import com.maplecan.scraper.model.ScreenshotData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URL;

@Slf4j
@Service
@RequiredArgsConstructor
public class BaselineImageService {

    private final SupabaseStorageService supabaseStorageService;

    // Memory management constants
    private static final int MAX_IMAGE_WIDTH = 2000;
    private static final int MAX_IMAGE_HEIGHT = 8000;
    private static final long MAX_IMAGE_PIXELS = 10_000_000L; // 10 million pixels max

    /**
     * Extract and save baseline image from screenshot based on user's selection coordinates
     * @param screenshotData The screenshot data containing coordinates and screenshot URL
     * @param jwtToken User's JWT token for authentication
     * @return The URL of the saved baseline image
     * @throws IOException If image processing or upload fails
     */
    public String extractAndSaveBaselineImage(ScreenshotData screenshotData, String jwtToken) throws IOException {
        log.info("Extracting baseline image for screenshot ID: {}", screenshotData.getId());
        
        if (screenshotData.getCoordinates() == null) {
            throw new IllegalArgumentException("Screenshot coordinates are required to extract baseline image");
        }
        
        // Load the original screenshot
        BufferedImage originalImage = loadImageFromUrl(screenshotData.getScreenshotUrl());
        if (originalImage == null) {
            throw new IOException("Failed to load original screenshot from URL: " + screenshotData.getScreenshotUrl());
        }
        log.info("Original image dimensions: {}x{}", originalImage.getWidth(), originalImage.getHeight());
        log.info("Coordinates: {}", screenshotData.getCoordinates());

        // Extract the selected area based on percentage coordinates
        BufferedImage croppedImage = extractSelectedArea(originalImage, screenshotData.getCoordinates());
        log.info("Cropped image dimensions: {}x{}", croppedImage.getWidth(), croppedImage.getHeight());

        // Ensure cropped image is within memory limits
        BufferedImage processedImage = ensureImageSizeWithinLimits(croppedImage);
        if (processedImage != croppedImage) {
            log.info("Cropped image downscaled to: {}x{}", processedImage.getWidth(), processedImage.getHeight());
        }

        // Convert to byte array
        byte[] imageBytes = convertImageToBytes(processedImage, "jpg");
        log.info("Image bytes length: {}", imageBytes.length);
        
        // Generate filename for baseline image
        String baselineFileName = generateBaselineFileName(screenshotData.getUserId(), screenshotData.getSupabaseId());
        
        // Upload to Supabase storage
        String baselineImageUrl = uploadBaselineImage(imageBytes, baselineFileName, jwtToken);
        
        log.info("Successfully saved baseline image: {}", baselineImageUrl);
        return baselineImageUrl;
    }

    /**
     * Create baseline image from the entire screenshot (when no coordinates are selected)
     */
    public String createFullImageBaseline(ScreenshotData screenshotData, String jwtToken) throws IOException {
        log.info("Creating full-image baseline for screenshot ID: {}", screenshotData.getId());

        // Load the original screenshot image
        BufferedImage originalImage = loadImageFromUrl(screenshotData.getScreenshotUrl());
        if (originalImage == null) {
            throw new IOException("Failed to load original screenshot from URL: " + screenshotData.getScreenshotUrl());
        }

        log.info("Original image dimensions: {}x{}", originalImage.getWidth(), originalImage.getHeight());

        // Check if image needs to be downscaled for memory efficiency
        BufferedImage processedImage = ensureImageSizeWithinLimits(originalImage);
        if (processedImage != originalImage) {
            log.info("Image downscaled to: {}x{}", processedImage.getWidth(), processedImage.getHeight());
        }

        // Use the entire image as baseline (no cropping)
        byte[] imageBytes = convertImageToBytes(processedImage, "jpg");
        log.info("Full image bytes length: {}", imageBytes.length);

        // Generate filename for baseline image
        String baselineFileName = generateBaselineFileName(screenshotData.getUserId(), screenshotData.getSupabaseId());

        // Upload to Supabase storage
        String baselineImageUrl = uploadBaselineImage(imageBytes, baselineFileName, jwtToken);

        log.info("Successfully saved full-image baseline: {}", baselineImageUrl);
        return baselineImageUrl;
    }

    /**
     * Load image from URL
     */
    private BufferedImage loadImageFromUrl(String imageUrl) {
        try {
            URL url = new URL(imageUrl);
            BufferedImage image = ImageIO.read(url);
            if (image == null) {
                log.error("ImageIO.read returned null for URL: {}", imageUrl);
                return null;
            }
            log.debug("Successfully loaded image: {}x{} from {}", 
                     image.getWidth(), image.getHeight(), imageUrl);
            return image;
        } catch (Exception e) {
            log.error("Failed to load image from URL: {}", imageUrl, e);
            return null;
        }
    }

    /**
     * Extract the selected area from the original image using percentage coordinates
     * Enhanced to account for device context and cross-device compatibility
     */
    private BufferedImage extractSelectedArea(BufferedImage originalImage, ScreenshotData.Coordinates coordinates) {
        int imageWidth = originalImage.getWidth();
        int imageHeight = originalImage.getHeight();

        log.debug("Processing image with dimensions: {}x{}", imageWidth, imageHeight);

        // Log device context if available for debugging
        if (coordinates.getDeviceInfo() != null) {
            ScreenshotData.DeviceInfo deviceInfo = coordinates.getDeviceInfo();
            log.debug("Original selection made on: {} device ({}x{} viewport, {}x{} image display)",
                     deviceInfo.getIsMobile() ? "mobile" : "desktop",
                     deviceInfo.getViewportWidth(), deviceInfo.getViewportHeight(),
                     deviceInfo.getImageDisplayWidth(), deviceInfo.getImageDisplayHeight());
        }

        // Use percentage coordinates if available, otherwise fall back to pixel coordinates
        int x, y, width, height;

        if (coordinates.getPercentageX() != null && coordinates.getPercentageY() != null &&
            coordinates.getPercentageWidth() != null && coordinates.getPercentageHeight() != null) {

            // Convert percentage to pixels based on current image dimensions
            // This ensures cross-device compatibility since percentages are device-independent
            x = (int) ((coordinates.getPercentageX() / 100.0) * imageWidth);
            y = (int) ((coordinates.getPercentageY() / 100.0) * imageHeight);
            width = (int) ((coordinates.getPercentageWidth() / 100.0) * imageWidth);
            height = (int) ((coordinates.getPercentageHeight() / 100.0) * imageHeight);

            log.debug("Using percentage coordinates: {}%, {}%, {}%, {}% -> {}x{} at ({}, {})",
                     coordinates.getPercentageX(), coordinates.getPercentageY(),
                     coordinates.getPercentageWidth(), coordinates.getPercentageHeight(),
                     width, height, x, y);
        } else {
            // Fall back to pixel coordinates (less reliable for cross-device comparison)
            x = (int) coordinates.getX();
            y = (int) coordinates.getY();
            width = (int) coordinates.getWidth();
            height = (int) coordinates.getHeight();

            log.warn("Using pixel coordinates (may not be accurate for cross-device comparison): {}, {}, {}, {}", x, y, width, height);

            // If device info is available, try to adjust pixel coordinates for current image size
            if (coordinates.getDeviceInfo() != null && coordinates.getDeviceInfo().getImageDisplayWidth() != null) {
                ScreenshotData.DeviceInfo deviceInfo = coordinates.getDeviceInfo();
                double scaleX = imageWidth / deviceInfo.getImageDisplayWidth();
                double scaleY = imageHeight / deviceInfo.getImageDisplayHeight();

                x = (int) (x * scaleX);
                y = (int) (y * scaleY);
                width = (int) (width * scaleX);
                height = (int) (height * scaleY);

                log.debug("Adjusted pixel coordinates for image scaling ({}x -> {}x): {}, {}, {}, {}",
                         deviceInfo.getImageDisplayWidth(), imageWidth, x, y, width, height);
            }
        }
        
        // Ensure coordinates are within image bounds
        x = Math.max(0, Math.min(x, imageWidth - 1));
        y = Math.max(0, Math.min(y, imageHeight - 1));
        width = Math.max(1, Math.min(width, imageWidth - x));
        height = Math.max(1, Math.min(height, imageHeight - y));
        
        log.debug("Final crop coordinates: x={}, y={}, width={}, height={}", x, y, width, height);
        
        // Extract the subimage
        return originalImage.getSubimage(x, y, width, height);
    }

    /**
     * Convert BufferedImage to byte array with memory-efficient processing
     */
    private byte[] convertImageToBytes(BufferedImage image, String format) throws IOException {
        if (image == null) {
            throw new IOException("Cannot convert null image to bytes");
        }

        log.info("Converting image - Width: {}, Height: {}, Type: {}",
                image.getWidth(), image.getHeight(), image.getType());

        // Check if image is already in a compatible format
        BufferedImage imageToWrite = image;

        // Only convert to RGB if necessary (to avoid memory issues with large images)
        if (image.getType() != BufferedImage.TYPE_INT_RGB &&
            image.getType() != BufferedImage.TYPE_3BYTE_BGR) {

            log.info("Converting image to RGB format for compatibility");
            imageToWrite = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = imageToWrite.createGraphics();

            // Set rendering hints for better performance
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_SPEED);
            g2d.setRenderingHint(RenderingHints.KEY_COLOR_RENDERING, RenderingHints.VALUE_COLOR_RENDER_SPEED);

            g2d.setColor(Color.WHITE); // Set white background
            g2d.fillRect(0, 0, image.getWidth(), image.getHeight());
            g2d.drawImage(image, 0, 0, null);
            g2d.dispose();
        }

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        boolean success = ImageIO.write(imageToWrite, format, baos);

        if (!success) {
            throw new IOException("Failed to write image in format: " + format);
        }

        byte[] result = baos.toByteArray();
        log.info("Successfully converted image to {} bytes in format: {}", result.length, format);

        // Clean up if we created a new image
        if (imageToWrite != image) {
            imageToWrite.flush();
        }

        return result;
    }

    /**
     * Ensure image size is within memory limits by downscaling if necessary
     */
    private BufferedImage ensureImageSizeWithinLimits(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();
        long totalPixels = (long) width * height;

        // Check if image is within limits
        if (width <= MAX_IMAGE_WIDTH && height <= MAX_IMAGE_HEIGHT && totalPixels <= MAX_IMAGE_PIXELS) {
            return image; // No scaling needed
        }

        log.info("Image exceeds memory limits ({}x{}, {} pixels). Downscaling...", width, height, totalPixels);

        // Calculate scale factor to fit within limits
        double scaleX = (double) MAX_IMAGE_WIDTH / width;
        double scaleY = (double) MAX_IMAGE_HEIGHT / height;
        double pixelScale = Math.sqrt((double) MAX_IMAGE_PIXELS / totalPixels);

        // Use the most restrictive scale factor
        double scale = Math.min(Math.min(scaleX, scaleY), pixelScale);

        int newWidth = (int) (width * scale);
        int newHeight = (int) (height * scale);

        log.info("Scaling image from {}x{} to {}x{} (scale factor: {:.3f})",
                width, height, newWidth, newHeight, scale);

        // Create scaled image
        BufferedImage scaledImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = scaledImage.createGraphics();

        // Set high-quality scaling hints
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // Draw scaled image
        g2d.drawImage(image, 0, 0, newWidth, newHeight, null);
        g2d.dispose();

        return scaledImage;
    }

    /**
     * Generate filename for baseline image
     */
    private String generateBaselineFileName(String userId, String supabaseId) {
        return userId + "/" + supabaseId + "/baseline.jpg";
    }

    /**
     * Upload baseline image to Supabase storage using user JWT token
     * Uses the same upload method as regular screenshots for consistency
     */
    private String uploadBaselineImage(byte[] imageBytes, String fileName, String jwtToken) throws IOException {
        try {
            // Create a temporary URL for the upload (we'll use the filename structure)
            // Extract userId and supabaseId from fileName (format: userId/supabaseId/baseline.jpg)
            String[] parts = fileName.split("/");
            if (parts.length >= 2) {
                String userId = parts[0];
                String supabaseId = parts[1];
                // Use the existing uploadScreenshotWithDataId method but with a custom filename
                return uploadBaselineImageWithCustomName(imageBytes, userId, supabaseId, fileName, jwtToken);
            } else {
                throw new IOException("Invalid baseline filename format: " + fileName);
            }
        } catch (Exception e) {
            log.error("Failed to upload baseline image: {}", fileName, e);
            throw new IOException("Failed to upload baseline image to storage", e);
        }
    }

    /**
     * Upload baseline image using the same HTTP connection logic as regular screenshots
     */
    private String uploadBaselineImageWithCustomName(byte[] imageBytes, String userId, String supabaseId, String fileName, String jwtToken) throws IOException {
        String baseUrl = "https://tovaxwkyjnrfssqzpmcl.supabase.co";

        // First try to delete existing file if it exists
        String deleteUrl = baseUrl + "/storage/v1/object/items/" + fileName;
        try {
            java.net.URL deleteUrlObj = new java.net.URL(deleteUrl);
            java.net.HttpURLConnection deleteConnection = (java.net.HttpURLConnection) deleteUrlObj.openConnection();
            deleteConnection.setRequestMethod("DELETE");
            deleteConnection.setRequestProperty("Authorization", "Bearer " + jwtToken);
            int deleteResponse = deleteConnection.getResponseCode();
            log.info("Delete existing baseline response: {}", deleteResponse);
        } catch (Exception e) {
            log.info("No existing baseline to delete or delete failed: {}", e.getMessage());
        }

        // Now upload the new file
        String uploadUrl = baseUrl + "/storage/v1/object/items/" + fileName;
        try {
            java.net.URL url_obj = new java.net.URL(uploadUrl);
            java.net.HttpURLConnection connection = (java.net.HttpURLConnection) url_obj.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Authorization", "Bearer " + jwtToken);
            connection.setRequestProperty("Content-Type", "image/jpeg");
            connection.setDoOutput(true);

            // Write the file data
            log.info("Writing {} bytes to Supabase", imageBytes.length);
            try (java.io.OutputStream os = connection.getOutputStream()) {
                os.write(imageBytes);
                os.flush();
            }

            int responseCode = connection.getResponseCode();
            if (responseCode == 200 || responseCode == 201) {
                // Return the public URL
                return baseUrl + "/storage/v1/object/public/items/" + fileName;
            } else {
                // Read error response
                StringBuilder errorResponse = new StringBuilder();
                try (java.io.BufferedReader br = new java.io.BufferedReader(new java.io.InputStreamReader(connection.getErrorStream()))) {
                    String line;
                    while ((line = br.readLine()) != null) {
                        errorResponse.append(line);
                    }
                }
                throw new IOException("Failed to upload baseline image to Supabase: " + responseCode + " - " + errorResponse.toString());
            }
        } catch (Exception e) {
            throw new IOException("Failed to upload baseline image with custom name", e);
        }
    }

    /**
     * Extract and save comparison image from new screenshot for change detection
     * @param newScreenshotUrl URL of the new screenshot
     * @param coordinates The same coordinates used for baseline
     * @param userId User ID for file organization
     * @param supabaseId Supabase ID for file organization
     * @param jwtToken User's JWT token for authentication
     * @return The URL of the saved comparison image
     * @throws IOException If image processing or upload fails
     */
    public String extractAndSaveComparisonImage(String newScreenshotUrl, ScreenshotData.Coordinates coordinates,
                                              String userId, String supabaseId, String jwtToken) throws IOException {
        log.info("Extracting comparison image from: {}", newScreenshotUrl);

        // Validate coordinates before processing
        validateCoordinatesForComparison(coordinates);

        // Load the new screenshot
        BufferedImage newImage = loadImageFromUrl(newScreenshotUrl);
        if (newImage == null) {
            throw new IOException("Failed to load new screenshot from URL: " + newScreenshotUrl);
        }

        log.info("New screenshot dimensions: {}x{}", newImage.getWidth(), newImage.getHeight());

        // Extract the same area as baseline
        BufferedImage croppedImage = extractSelectedArea(newImage, coordinates);

        // Ensure cropped image is within memory limits
        BufferedImage processedImage = ensureImageSizeWithinLimits(croppedImage);
        if (processedImage != croppedImage) {
            log.info("Comparison image downscaled to: {}x{}", processedImage.getWidth(), processedImage.getHeight());
        }

        // Convert to byte array
        byte[] imageBytes = convertImageToBytes(processedImage, "jpg");
        
        // Generate filename for comparison image with timestamp
        String comparisonFileName = generateComparisonFileName(userId, supabaseId);
        
        // Upload to Supabase storage
        String comparisonImageUrl = uploadBaselineImage(imageBytes, comparisonFileName, jwtToken);
        
        log.info("Successfully saved comparison image: {}", comparisonImageUrl);
        return comparisonImageUrl;
    }

    /**
     * Generate filename for comparison image with timestamp
     */
    private String generateComparisonFileName(String userId, String supabaseId) {
        long timestamp = System.currentTimeMillis();
        return userId + "/" + supabaseId + "/comparison-" + timestamp + ".jpg";
    }

    /**
     * Validate coordinates to ensure we have the right data for comparison
     */
    private void validateCoordinatesForComparison(ScreenshotData.Coordinates coordinates) throws IOException {
        if (coordinates == null) {
            throw new IOException("No coordinates available for comparison");
        }

        // Check if we have percentage coordinates (preferred for cross-device compatibility)
        boolean hasPercentageCoords = coordinates.getPercentageX() != null &&
                                    coordinates.getPercentageY() != null &&
                                    coordinates.getPercentageWidth() != null &&
                                    coordinates.getPercentageHeight() != null;

        // Check if we have pixel coordinates (fallback)
        boolean hasPixelCoords = coordinates.getX() > 0 || coordinates.getY() > 0 ||
                               coordinates.getWidth() > 0 || coordinates.getHeight() > 0;

        if (!hasPercentageCoords && !hasPixelCoords) {
            throw new IOException("No valid coordinates found for comparison");
        }

        if (hasPercentageCoords) {
            log.info("Using percentage-based coordinates for cross-device compatible comparison");

            // Validate percentage ranges
            if (coordinates.getPercentageX() < 0 || coordinates.getPercentageX() > 100 ||
                coordinates.getPercentageY() < 0 || coordinates.getPercentageY() > 100 ||
                coordinates.getPercentageWidth() <= 0 || coordinates.getPercentageWidth() > 100 ||
                coordinates.getPercentageHeight() <= 0 || coordinates.getPercentageHeight() > 100) {
                throw new IOException("Invalid percentage coordinates: values must be between 0-100");
            }
        } else {
            log.warn("Using pixel-based coordinates - may not be accurate for cross-device comparison");

            if (coordinates.getDeviceInfo() == null) {
                log.warn("No device context available - pixel coordinate accuracy may be compromised");
            } else {
                log.info("Device context available for pixel coordinate adjustment");
            }
        }

        // Log device context for debugging
        if (coordinates.getDeviceInfo() != null) {
            ScreenshotData.DeviceInfo deviceInfo = coordinates.getDeviceInfo();
            log.debug("Coordinate device context: {} device, viewport: {}x{}, image display: {}x{}",
                     deviceInfo.getIsMobile() ? "mobile" : "desktop",
                     deviceInfo.getViewportWidth(), deviceInfo.getViewportHeight(),
                     deviceInfo.getImageDisplayWidth(), deviceInfo.getImageDisplayHeight());
        }
    }
}
