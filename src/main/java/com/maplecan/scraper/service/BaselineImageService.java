package com.maplecan.scraper.service;

import com.maplecan.scraper.model.ScreenshotData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URL;

@Slf4j
@Service
@RequiredArgsConstructor
public class BaselineImageService {

    private final SupabaseStorageService supabaseStorageService;

    /**
     * Extract and save baseline image from screenshot based on user's selection coordinates
     * @param screenshotData The screenshot data containing coordinates and screenshot URL
     * @param jwtToken User's JWT token for authentication
     * @return The URL of the saved baseline image
     * @throws IOException If image processing or upload fails
     */
    public String extractAndSaveBaselineImage(ScreenshotData screenshotData, String jwtToken) throws IOException {
        log.info("Extracting baseline image for screenshot ID: {}", screenshotData.getId());
        
        if (screenshotData.getCoordinates() == null) {
            throw new IllegalArgumentException("Screenshot coordinates are required to extract baseline image");
        }
        
        // Load the original screenshot
        BufferedImage originalImage = loadImageFromUrl(screenshotData.getScreenshotUrl());
        if (originalImage == null) {
            throw new IOException("Failed to load original screenshot from URL: " + screenshotData.getScreenshotUrl());
        }
        
        // Extract the selected area based on percentage coordinates
        BufferedImage croppedImage = extractSelectedArea(originalImage, screenshotData.getCoordinates());
        
        // Convert to byte array
        byte[] imageBytes = convertImageToBytes(croppedImage, "jpg");
        
        // Generate filename for baseline image
        String baselineFileName = generateBaselineFileName(screenshotData.getUserId(), screenshotData.getSupabaseId());
        
        // Upload to Supabase storage
        String baselineImageUrl = uploadBaselineImage(imageBytes, baselineFileName, jwtToken);
        
        log.info("Successfully saved baseline image: {}", baselineImageUrl);
        return baselineImageUrl;
    }

    /**
     * Load image from URL
     */
    private BufferedImage loadImageFromUrl(String imageUrl) {
        try {
            URL url = new URL(imageUrl);
            BufferedImage image = ImageIO.read(url);
            if (image == null) {
                log.error("ImageIO.read returned null for URL: {}", imageUrl);
                return null;
            }
            log.debug("Successfully loaded image: {}x{} from {}", 
                     image.getWidth(), image.getHeight(), imageUrl);
            return image;
        } catch (Exception e) {
            log.error("Failed to load image from URL: {}", imageUrl, e);
            return null;
        }
    }

    /**
     * Extract the selected area from the original image using percentage coordinates
     */
    private BufferedImage extractSelectedArea(BufferedImage originalImage, ScreenshotData.Coordinates coordinates) {
        int imageWidth = originalImage.getWidth();
        int imageHeight = originalImage.getHeight();
        
        // Use percentage coordinates if available, otherwise fall back to pixel coordinates
        int x, y, width, height;
        
        if (coordinates.getPercentageX() != null && coordinates.getPercentageY() != null &&
            coordinates.getPercentageWidth() != null && coordinates.getPercentageHeight() != null) {
            
            // Convert percentage to pixels
            x = (int) ((coordinates.getPercentageX() / 100.0) * imageWidth);
            y = (int) ((coordinates.getPercentageY() / 100.0) * imageHeight);
            width = (int) ((coordinates.getPercentageWidth() / 100.0) * imageWidth);
            height = (int) ((coordinates.getPercentageHeight() / 100.0) * imageHeight);
            
            log.debug("Using percentage coordinates: {}%, {}%, {}%, {}%", 
                     coordinates.getPercentageX(), coordinates.getPercentageY(),
                     coordinates.getPercentageWidth(), coordinates.getPercentageHeight());
        } else {
            // Fall back to pixel coordinates
            x = (int) coordinates.getX();
            y = (int) coordinates.getY();
            width = (int) coordinates.getWidth();
            height = (int) coordinates.getHeight();
            
            log.debug("Using pixel coordinates: {}, {}, {}, {}", x, y, width, height);
        }
        
        // Ensure coordinates are within image bounds
        x = Math.max(0, Math.min(x, imageWidth - 1));
        y = Math.max(0, Math.min(y, imageHeight - 1));
        width = Math.max(1, Math.min(width, imageWidth - x));
        height = Math.max(1, Math.min(height, imageHeight - y));
        
        log.debug("Final crop coordinates: x={}, y={}, width={}, height={}", x, y, width, height);
        
        // Extract the subimage
        return originalImage.getSubimage(x, y, width, height);
    }

    /**
     * Convert BufferedImage to byte array
     */
    private byte[] convertImageToBytes(BufferedImage image, String format) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, format, baos);
        return baos.toByteArray();
    }

    /**
     * Generate filename for baseline image
     */
    private String generateBaselineFileName(String userId, String supabaseId) {
        return userId + "/" + supabaseId + "/baseline.jpg";
    }

    /**
     * Upload baseline image to Supabase storage using user JWT token
     * Uses the same upload method as regular screenshots for consistency
     */
    private String uploadBaselineImage(byte[] imageBytes, String fileName, String jwtToken) throws IOException {
        try {
            // Create a temporary URL for the upload (we'll use the filename structure)
            // Extract userId and supabaseId from fileName (format: userId/supabaseId/baseline.jpg)
            String[] parts = fileName.split("/");
            if (parts.length >= 2) {
                String userId = parts[0];
                String supabaseId = parts[1];
                // Use the existing uploadScreenshotWithDataId method but with a custom filename
                return uploadBaselineImageWithCustomName(imageBytes, userId, supabaseId, fileName, jwtToken);
            } else {
                throw new IOException("Invalid baseline filename format: " + fileName);
            }
        } catch (Exception e) {
            log.error("Failed to upload baseline image: {}", fileName, e);
            throw new IOException("Failed to upload baseline image to storage", e);
        }
    }

    /**
     * Upload baseline image using the same HTTP connection logic as regular screenshots
     */
    private String uploadBaselineImageWithCustomName(byte[] imageBytes, String userId, String supabaseId, String fileName, String jwtToken) throws IOException {
        String baseUrl = "https://tovaxwkyjnrfssqzpmcl.supabase.co";
        String uploadUrl = baseUrl + "/storage/v1/object/items/" + fileName;

        try {
            java.net.URL url_obj = new java.net.URL(uploadUrl);
            java.net.HttpURLConnection connection = (java.net.HttpURLConnection) url_obj.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Authorization", "Bearer " + jwtToken);
            connection.setRequestProperty("Content-Type", "image/jpeg");
            connection.setDoOutput(true);

            // Write the file data
            try (java.io.OutputStream os = connection.getOutputStream()) {
                os.write(imageBytes);
                os.flush();
            }

            int responseCode = connection.getResponseCode();
            if (responseCode == 200 || responseCode == 201) {
                // Return the public URL
                return baseUrl + "/storage/v1/object/public/items/" + fileName;
            } else {
                // Read error response
                StringBuilder errorResponse = new StringBuilder();
                try (java.io.BufferedReader br = new java.io.BufferedReader(new java.io.InputStreamReader(connection.getErrorStream()))) {
                    String line;
                    while ((line = br.readLine()) != null) {
                        errorResponse.append(line);
                    }
                }
                throw new IOException("Failed to upload baseline image to Supabase: " + responseCode + " - " + errorResponse.toString());
            }
        } catch (Exception e) {
            throw new IOException("Failed to upload baseline image with custom name", e);
        }
    }

    /**
     * Extract and save comparison image from new screenshot for change detection
     * @param newScreenshotUrl URL of the new screenshot
     * @param coordinates The same coordinates used for baseline
     * @param userId User ID for file organization
     * @param supabaseId Supabase ID for file organization
     * @param jwtToken User's JWT token for authentication
     * @return The URL of the saved comparison image
     * @throws IOException If image processing or upload fails
     */
    public String extractAndSaveComparisonImage(String newScreenshotUrl, ScreenshotData.Coordinates coordinates, 
                                              String userId, String supabaseId, String jwtToken) throws IOException {
        log.info("Extracting comparison image from: {}", newScreenshotUrl);
        
        // Load the new screenshot
        BufferedImage newImage = loadImageFromUrl(newScreenshotUrl);
        if (newImage == null) {
            throw new IOException("Failed to load new screenshot from URL: " + newScreenshotUrl);
        }
        
        // Extract the same area as baseline
        BufferedImage croppedImage = extractSelectedArea(newImage, coordinates);
        
        // Convert to byte array
        byte[] imageBytes = convertImageToBytes(croppedImage, "jpg");
        
        // Generate filename for comparison image with timestamp
        String comparisonFileName = generateComparisonFileName(userId, supabaseId);
        
        // Upload to Supabase storage
        String comparisonImageUrl = uploadBaselineImage(imageBytes, comparisonFileName, jwtToken);
        
        log.info("Successfully saved comparison image: {}", comparisonImageUrl);
        return comparisonImageUrl;
    }

    /**
     * Generate filename for comparison image with timestamp
     */
    private String generateComparisonFileName(String userId, String supabaseId) {
        long timestamp = System.currentTimeMillis();
        return userId + "/" + supabaseId + "/comparison-" + timestamp + ".jpg";
    }
}
