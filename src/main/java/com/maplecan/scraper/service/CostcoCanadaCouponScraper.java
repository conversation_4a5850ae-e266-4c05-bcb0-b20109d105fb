package com.maplecan.scraper.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.maplecan.scraper.model.Flyers;
import com.maplecan.scraper.model.CouponItem;
import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.ParseException;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;

@Service
public class CostcoCanadaCouponScraper {

    @Value("${scrapingbee.api.key}")
    private String apiKey;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Scrapes Costco Coupons using Apache HttpClient 5.x and ScrapingBee.
     */
    public Flyers scrapeCoupons(String postalCode) throws IOException {
        String apiUrl = "https://app.scrapingbee.com/api/v1/";
        String targetUrl = "https://www.costco.ca/coupons.html";

        // JavaScript Scenario for ScrapingBee
        String jsScenario = """
            {
                "instructions": [
                    { "wait": 10000 },
                    { "click": "[data-bi-tc*='ui:Delivery Location Flyout | Update']" },
                    { "wait": 2000 },
                    { "fill": ["#zipCode", "%s"] },
                    { "wait": 1000 },
                    { "click": "[data-bi-tc*='ui:Change Delivery Location | ']" },
                    { "wait": 5000 }
                ]
            }
            """.formatted(postalCode);

        // Construct the final request URL
        String requestUrl = String.format("%s?api_key=%s&url=%s&stealth_proxy=true&country_code=ca&js_scenario=%s",
                apiUrl, apiKey, encodeValue(targetUrl), encodeValue(jsScenario));

        // Make the HTTP request and get the response HTML
        String htmlContent = fetchHtmlContent(requestUrl);

        // Parse the coupon data
        return parseCoupons(htmlContent);
    }

    public void scrapeCouponsWithScreenshot(String postalCode) throws IOException {
        String apiUrl = "https://app.scrapingbee.com/api/v1/";
        String targetUrl = "https://www.costco.ca/coupons.html";

        // JavaScript Scenario for ScrapingBee
        String jsScenario = """
        {
            "instructions": [
                { "wait": 10000 },
                { "click": "[data-bi-tc*='ui:Delivery Location Flyout | Update']" },
                { "wait": 2000 },
                { "fill": ["#zipCode", "%s"] },
                { "wait": 1000 },
                { "click": "[data-bi-tc*='ui:Change Delivery Location | ']" },
                { "wait": 5000 }
            ]
        }
        """.formatted(postalCode);

        // Construct the final request URL with screenshot enabled
        String requestUrl = String.format("%s?api_key=%s&url=%s&stealth_proxy=true&country_code=ca&js_scenario=%s&screenshot_full_page=true",
                apiUrl, apiKey, encodeValue(targetUrl), encodeValue(jsScenario));

        // Save the screenshot locally
        fetchScreenshot(requestUrl, "./screenshot-2.jpg");
    }

    private void fetchScreenshot(String requestUrl, String outputFilePath) throws IOException {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet request = new HttpGet(requestUrl);
            try (CloseableHttpResponse response = httpClient.execute(request);
                 InputStream inputStream = response.getEntity().getContent();
                 FileOutputStream outputStream = new FileOutputStream(outputFilePath)) {

                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }

                System.out.println("Screenshot saved to: " + outputFilePath);
            }
        }
    }



    /**
     * Fetches HTML content from ScrapingBee using Apache HttpClient 5.x.
     */
    private String fetchHtmlContent(String requestUrl) throws IOException {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet request = new HttpGet(requestUrl);
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                return EntityUtils.toString(response.getEntity());
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        }
    }

    private Flyers parseCoupons(String htmlContent) {
        Document doc = Jsoup.parse(htmlContent);

        Flyers flyers = new Flyers();
        flyers.setFlyer("Warehouse savings");
        flyers.setVendor("COSTCOCANADA");
        flyers.setCreatedTimestamp(LocalDateTime.now());
        flyers.setLastUpdatedTimestamp(LocalDateTime.now());

        // Extract ValidFrom and ValidTo
        Element validDateRange = doc.selectFirst(".CLP-validdates");
        if (validDateRange != null) {
            Elements timeElements = validDateRange.select("time");
            if (timeElements.size() >= 2) {
                flyers.setValidFrom(timeElements.get(0).attr("datetime"));
                flyers.setValidTo(timeElements.get(1).attr("datetime"));
            }
        }

        // Extract coupon items
        List<CouponItem> items = new ArrayList<>();
        Elements couponElements = doc.select(".couponbox"); // Select all coupon containers

        for (Element coupon : couponElements) {
            CouponItem item = new CouponItem();

            // Extract Product Name
            Element productNameElement = coupon.selectFirst(".spanBlock.sl1");
            item.setProductName(Optional.ofNullable(productNameElement).map(Element::text).orElse(null));

            // Extract Item Number
            Element itemNumberElement = coupon.selectFirst(".sku");
            item.setItemNumber(Optional.ofNullable(itemNumberElement).map(el -> el.text().replace("Item number", "").trim()).orElse(null));

            // Extract Item URL
            Element itemUrlElement = coupon.selectFirst(".CLP-productimg a");
            item.setItemUrl(Optional.ofNullable(itemUrlElement).map(el -> el.attr("href")).orElse(null));

            // Extract Image URL
            Element imageElement = coupon.selectFirst(".CLP-productimg img");
            item.setImageUrl(Optional.ofNullable(imageElement).map(el -> el.attr("src")).orElse(null));

            // Determine if Online Offer Exists
            item.setOnline(coupon.selectFirst(".onlineOffer") != null);

            // Determine if Warehouse Only Offer
            item.setWarehouseOnly(coupon.selectFirst(".btn-whsOnly") != null);

            // Extract Action (e.g., "SAVE", "HOT BUY")
            Element actionElement = coupon.selectFirst(".spanBlock.action");
            String actionText = Optional.ofNullable(actionElement).map(Element::text).orElse("").trim();

            // Extract Price from "action" block
            Element priceElement = coupon.selectFirst(".spanBlock.price");
            String extractedPrice = Optional.ofNullable(priceElement).map(el -> el.text().replace("$", "").trim()).orElse(null);

            // Extract Pricing Table
            Elements priceRows = coupon.select(".eco-priceTableGrid tr");

            String warehousePrice = null;
            String ecoFee = null;
            String finalPrice = null;
            String instantSavings = null;

            for (Element row : priceRows) {
                Elements columns = row.select("td");

                if (columns.size() == 2) {
                    String label = columns.get(0).text().trim();
                    String value = columns.get(1).text().replace("$", "").trim();

                    if (label.startsWith("In-warehouse")) {
                        warehousePrice = value;
                    } else if (label.startsWith("Eco fee")) {
                        ecoFee = value.replace("+", ""); // Remove "+" from Eco Fee
                    } else if (label.startsWith("Instant savings")) {
                        instantSavings = "-" + value; // Ensure it's negative
                    } else if (label.startsWith("PRICE")) {
                        finalPrice = value;
                    }
                }
            }

            // If the final price is missing, check if it's a "HOT BUY" and use that price
            if (finalPrice == null && "HOT BUY".equalsIgnoreCase(actionText)) {
                finalPrice = extractedPrice;
            }

            // If instant savings is missing but it's a "SAVE" coupon, use extracted price as savings
            if (instantSavings == null && "SAVE".equalsIgnoreCase(actionText)) {
                instantSavings = "-" + extractedPrice;
            }

            // If instant savings exists, normalize dashes and remove any leading "-"
            if (instantSavings != null) {
                instantSavings = instantSavings.replaceAll("[–—−]", "-") // Replace en dash, em dash, and minus sign with hyphen
                        .replaceAll("^-+", "")    // Remove leading hyphens
                        .trim();
            }

            // Assign extracted values with Optionals
            item.setWarehousePrice(warehousePrice);
            item.setEcoFee(ecoFee);
            item.setPrice(finalPrice);
            item.setInstantSavings(instantSavings);

            // Assign ValidFrom and ValidTo from CouponData
            item.setValidFrom(flyers.getValidFrom());
            item.setValidTo(flyers.getValidTo());

            items.add(item);
        }

        flyers.setItems(items);
        return flyers;
    }

    /**
     * Encodes a value for URL usage.
     */
    private static String encodeValue(String value) {
        return URLEncoder.encode(value, StandardCharsets.UTF_8);
    }
}
