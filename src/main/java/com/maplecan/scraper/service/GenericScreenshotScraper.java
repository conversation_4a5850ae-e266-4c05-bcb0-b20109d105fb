package com.maplecan.scraper.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * Generic screenshot scraper that can handle any URL using ScrapingBee
 * with optimized parameters for screenshot capture
 */
@Slf4j
@Service
public class GenericScreenshotScraper implements ScreenshotScraper {

    @Value("${scrapingbee.api.key}")
    private String apiKey;

    @Override
    public byte[] captureScreenshot(String url) throws IOException {
        log.info("Capturing screenshot for URL: {}", url);
        
        String apiUrl = "https://app.scrapingbee.com/api/v1/";
        
        // Build the request URL with optimized parameters for screenshot capture
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(apiUrl)
                .queryParam("api_key", apiKey)
                .queryParam("render_js", true)
                .queryParam("premium_proxy", true)
                .queryParam("url", url)
                .queryParam("block_resources", false)
                .queryParam("country_code", "ca")
                .queryParam("stealth_proxy", true)
                .queryParam("screenshot_full_page", true)
                .queryParam("wait", 30000); // Wait 30 seconds for page to fully load

        String requestUrl = uriBuilder.toUriString();
        log.debug("ScrapingBee request URL: {}", requestUrl);

        return fetchScreenshotBytes(requestUrl);
    }

    @Override
    public boolean supports(String url) {
        // This generic scraper supports any valid HTTP/HTTPS URL
        return url != null && (url.startsWith("http://") || url.startsWith("https://"));
    }

    /**
     * Fetches screenshot bytes from ScrapingBee API
     * @param requestUrl The complete ScrapingBee API URL with parameters
     * @return byte array containing the screenshot image data
     * @throws IOException if the request fails
     */
    private byte[] fetchScreenshotBytes(String requestUrl) throws IOException {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet request = new HttpGet(requestUrl);
            
            try (CloseableHttpResponse response = httpClient.execute(request);
                 InputStream inputStream = response.getEntity().getContent();
                 ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

                // Check response status
                int statusCode = response.getCode();
                if (statusCode != 200) {
                    log.error("ScrapingBee API returned status code: {} for URL: {}", statusCode, requestUrl);
                    throw new IOException("Failed to fetch website screenshot. Please try again");
                }

                // Read the image data
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }

                byte[] screenshotBytes = outputStream.toByteArray();
                log.info("Successfully captured screenshot, size: {} bytes", screenshotBytes.length);
                
                return screenshotBytes;
            }
        }
    }

    /**
     * URL encodes a value for safe inclusion in query parameters
     * @param value The value to encode
     * @return URL encoded string
     */
    private String encodeValue(String value) {
        return URLEncoder.encode(value, StandardCharsets.UTF_8);
    }
}
