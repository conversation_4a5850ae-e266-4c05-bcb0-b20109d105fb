package com.maplecan.scraper.service;

import com.maplecan.scraper.service.ScraperFactory;
import com.maplecan.scraper.model.ScreenshotData;
import com.maplecan.scraper.model.ScreenshotResponse;
import com.maplecan.scraper.repository.ScreenshotDataRepository;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;

@Service
public class ScreenshotService {

    @Value("${scrapingbee.api.key}")
    private String apiKey;

    private final ScraperFactory scraperFactory;
    private final SupabaseStorageService supabaseStorageService;
    private final ScreenshotDataRepository screenshotDataRepository;

    public ScreenshotService(ScraperFactory scraperFactory,
                           SupabaseStorageService supabaseStorageService,
                           ScreenshotDataRepository screenshotDataRepository) {
        this.scraperFactory = scraperFactory;
        this.supabaseStorageService = supabaseStorageService;
        this.screenshotDataRepository = screenshotDataRepository;
    }

    /**
     * Captures a screenshot of the given URL and saves it to Supabase storage
     * @param url The URL to capture
     * @return ScreenshotResponse with screenshot details
     * @throws IOException if screenshot capture fails
     */
    public ScreenshotResponse captureScreenshot(String url) throws IOException {
        // Validate that we have a scraper for this URL
        try {
            scraperFactory.getScraper(url);
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Unsupported URL: " + url);
        }

        // Use ScrapingBee to capture screenshot
        String apiUrl = "https://app.scrapingbee.com/api/v1/";
        String requestUrl = String.format("%s?api_key=%s&url=%s&screenshot_full_page=true&stealth_proxy=true",
                apiUrl, apiKey, encodeValue(url));

        byte[] screenshotBytes = fetchScreenshotBytes(requestUrl);

        // Upload to Supabase storage (using service key for anonymous uploads)
        String screenshotUrl = supabaseStorageService.uploadFile(screenshotBytes, "anonymous/" + System.currentTimeMillis() + ".jpg", "image/jpeg");

        return ScreenshotResponse.builder()
                .screenshotUrl(screenshotUrl)
                .originalUrl(url)
                .capturedAt(LocalDateTime.now())
                .status("success")
                .message("Screenshot captured successfully")
                .build();
    }

    /**
     * Captures a screenshot and saves metadata to database
     * @param url The URL to capture
     * @param userId The user ID
     * @param userEmail The user email
     * @return ScreenshotData with saved metadata including ID
     * @throws IOException if screenshot capture fails
     */
    public ScreenshotData captureScreenshotWithMetadata(String url, String userId, String userEmail, String jwtToken) throws IOException {
        // Validate that we have a scraper for this URL
        try {
            scraperFactory.getScraper(url);
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Unsupported URL: " + url);
        }

        // Use ScrapingBee to capture screenshot
        String apiUrl = "https://app.scrapingbee.com/api/v1/";
        String requestUrl = String.format("%s?api_key=%s&url=%s&screenshot_full_page=true&stealth_proxy=true",
                apiUrl, apiKey, encodeValue(url));

        byte[] screenshotBytes = fetchScreenshotBytes(requestUrl);

        // Upload to Supabase storage with user-specific path
        String screenshotUrl = supabaseStorageService.uploadScreenshot(screenshotBytes, userId, url, jwtToken);

        // Save metadata to database
        ScreenshotData screenshotData = ScreenshotData.builder()
                .url(url)
                .screenshotUrl(screenshotUrl)
                .userId(userId)
                .userEmail(userEmail)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        return screenshotDataRepository.save(screenshotData);
    }

    private byte[] fetchScreenshotBytes(String requestUrl) throws IOException {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet request = new HttpGet(requestUrl);
            try (CloseableHttpResponse response = httpClient.execute(request);
                 InputStream inputStream = response.getEntity().getContent();
                 ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }

                return outputStream.toByteArray();
            }
        }
    }

    private String encodeValue(String value) {
        return URLEncoder.encode(value, StandardCharsets.UTF_8);
    }
}
