package com.maplecan.scraper.service;

import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

@Service
public class ScreenshotService {

    @Value("${scrapingbee.api.key}")
    private String apiKey;

    private final ScraperFactory scraperFactory;

    public ScreenshotService(ScraperFactory scraperFactory) {
        this.scraperFactory = scraperFactory;
    }

    /**
     * Captures a screenshot of the given URL and returns the file path
     * @param url The URL to capture
     * @return The file path where the screenshot is saved
     * @throws IOException if screenshot capture fails
     */
    public String captureScreenshot(String url) throws IOException {
        // Validate that we have a scraper for this URL
        try {
            scraperFactory.getScraper(url);
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Unsupported URL: " + url);
        }

        // Generate unique filename
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String uniqueId = UUID.randomUUID().toString().substring(0, 8);
        String filename = String.format("screenshot_%s_%s.jpg", timestamp, uniqueId);
        
        // Create screenshots directory if it doesn't exist
        Path screenshotsDir = Paths.get("screenshots");
        if (!Files.exists(screenshotsDir)) {
            Files.createDirectories(screenshotsDir);
        }
        
        String filePath = screenshotsDir.resolve(filename).toString();

        // Use ScrapingBee to capture screenshot
        String apiUrl = "https://app.scrapingbee.com/api/v1/";
        String requestUrl = String.format("%s?api_key=%s&url=%s&screenshot_full_page=true&stealth_proxy=true",
                apiUrl, apiKey, encodeValue(url));

        fetchScreenshot(requestUrl, filePath);
        
        return filePath;
    }

    /**
     * Captures a screenshot and returns a public URL
     * @param url The URL to capture
     * @return A public URL to access the screenshot
     * @throws IOException if screenshot capture fails
     */
    public String captureScreenshotAndGetUrl(String url) throws IOException {
        String filePath = captureScreenshot(url);
        
        // For now, return a relative path that can be served by the application
        // In production, you might want to upload to cloud storage and return that URL
        String filename = Paths.get(filePath).getFileName().toString();
        return "/screenshots/" + filename;
    }

    private void fetchScreenshot(String requestUrl, String outputFilePath) throws IOException {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet request = new HttpGet(requestUrl);
            try (CloseableHttpResponse response = httpClient.execute(request);
                 InputStream inputStream = response.getEntity().getContent();
                 FileOutputStream outputStream = new FileOutputStream(outputFilePath)) {

                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }

                System.out.println("Screenshot saved to: " + outputFilePath);
            }
        }
    }

    private String encodeValue(String value) {
        return URLEncoder.encode(value, StandardCharsets.UTF_8);
    }
}
