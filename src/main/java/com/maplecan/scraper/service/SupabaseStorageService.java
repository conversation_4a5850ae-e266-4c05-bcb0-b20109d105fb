package com.maplecan.scraper.service;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.util.Base64;
import java.util.UUID;

@Service
public class SupabaseStorageService {

    @Value("${supabase.url}")
    private String supabaseUrl;

    @Value("${supabase.jwt.secret}")
    private String supabaseServiceKey;

    private static final String BUCKET_NAME = "items";

    /**
     * Upload a file to Supabase storage
     * @param fileBytes The file content as byte array
     * @param fileName The name of the file
     * @param contentType The MIME type of the file
     * @return The public URL of the uploaded file
     * @throws IOException If upload fails
     */
    public String uploadFile(byte[] fileBytes, String fileName, String contentType) throws IOException {
        // Extract base URL from auth URL
        String baseUrl = supabaseUrl.replace("/auth/v1", "");
        String uploadUrl = baseUrl + "/storage/v1/object/" + BUCKET_NAME + "/" + fileName;

        HttpURLConnection connection = (HttpURLConnection) new URL(uploadUrl).openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Authorization", "Bearer " + supabaseServiceKey);
        connection.setRequestProperty("Content-Type", contentType);
        connection.setDoOutput(true);

        // Write file data
        try (OutputStream os = connection.getOutputStream()) {
            os.write(fileBytes);
        }

        int responseCode = connection.getResponseCode();
        if (responseCode != 200 && responseCode != 201) {
            // Read error response
            try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getErrorStream()))) {
                StringBuilder errorResponse = new StringBuilder();
                String line;
                while ((line = br.readLine()) != null) {
                    errorResponse.append(line);
                }
                throw new IOException("Failed to upload file to Supabase: " + responseCode + " - " + errorResponse.toString());
            }
        }

        // Return the public URL
        return baseUrl + "/storage/v1/object/public/" + BUCKET_NAME + "/" + fileName;
    }

    /**
     * Upload a screenshot file with a generated filename
     * @param fileBytes The screenshot content as byte array
     * @return The public URL of the uploaded screenshot
     * @throws IOException If upload fails
     */
    public String uploadScreenshot(byte[] fileBytes) throws IOException {
        String fileName = "screenshots/" + generateScreenshotFileName();
        return uploadFile(fileBytes, fileName, "image/jpeg");
    }

    /**
     * Generate a unique filename for screenshots
     * @return A unique filename with timestamp and UUID
     */
    private String generateScreenshotFileName() {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String uuid = UUID.randomUUID().toString().substring(0, 8);
        return "screenshot_" + timestamp + "_" + uuid + ".jpg";
    }

    /**
     * Delete a file from Supabase storage
     * @param fileName The name of the file to delete
     * @throws IOException If deletion fails
     */
    public void deleteFile(String fileName) throws IOException {
        String baseUrl = supabaseUrl.replace("/auth/v1", "");
        String deleteUrl = baseUrl + "/storage/v1/object/" + BUCKET_NAME + "/" + fileName;

        HttpURLConnection connection = (HttpURLConnection) new URL(deleteUrl).openConnection();
        connection.setRequestMethod("DELETE");
        connection.setRequestProperty("Authorization", "Bearer " + supabaseServiceKey);

        int responseCode = connection.getResponseCode();
        if (responseCode != 200 && responseCode != 204) {
            throw new IOException("Failed to delete file from Supabase: " + responseCode);
        }
    }
}
