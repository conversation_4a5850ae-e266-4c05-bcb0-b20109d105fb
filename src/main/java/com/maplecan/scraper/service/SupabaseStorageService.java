package com.maplecan.scraper.service;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;

@Service
public class SupabaseStorageService {

    @Value("${supabase.url}")
    private String supabaseUrl;

    @Value("${supabase.service.key:}")
    private String supabaseServiceKey;

    @Value("${s3.access.key.id}")
    private String accessKeyId;

    @Value("${s3.secret.access.key}")
    private String secretAccessKey;

    @Value("${s3.endpoint}")
    private String endpoint;

    @Value("${s3.region}")
    private String region;

    @Value("${s3.bucket.name}")
    private String bucketName;

    /**
     * Upload a file to Supabase storage
     * @param fileBytes The file content as byte array
     * @param fileName The name of the file
     * @param contentType The MIME type of the file
     * @return The public URL of the uploaded file
     * @throws IOException If upload fails
     */
    public String uploadFile(byte[] fileBytes, String fileName, String contentType) throws IOException {
        // Extract base URL from auth URL
        String baseUrl = supabaseUrl.replace("/auth/v1", "");
        String uploadUrl = baseUrl + "/storage/v1/object/" + bucketName + "/" + fileName;

        HttpURLConnection connection = (HttpURLConnection) new URL(uploadUrl).openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Authorization", "Bearer " + supabaseServiceKey);
        connection.setRequestProperty("Content-Type", contentType);
        connection.setDoOutput(true);

        // Write file data
        try (OutputStream os = connection.getOutputStream()) {
            os.write(fileBytes);
        }

        int responseCode = connection.getResponseCode();
        if (responseCode != 200 && responseCode != 201) {
            // Read error response
            try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getErrorStream()))) {
                StringBuilder errorResponse = new StringBuilder();
                String line;
                while ((line = br.readLine()) != null) {
                    errorResponse.append(line);
                }
                throw new IOException("Failed to upload file to Supabase: " + responseCode + " - " + errorResponse.toString());
            }
        }

        // Return the public URL
        return baseUrl + "/storage/v1/object/public/" + bucketName + "/" + fileName;
    }

    /**
     * Upload a screenshot file with a generated filename using S3 API
     * @param fileBytes The screenshot content as byte array
     * @param userId The user ID to organize files by user
     * @param url The original URL being captured
     * @return The public URL of the uploaded screenshot
     * @throws RuntimeException If upload fails
     */
    public String uploadScreenshot(byte[] fileBytes, String userId, String url) {
        String fileName = generateScreenshotFileName(userId, url);

        try {
            // Create S3 client with Supabase credentials
            AwsBasicCredentials credentials = AwsBasicCredentials.create(accessKeyId, secretAccessKey);
            S3Client s3Client = S3Client.builder()
                    .credentialsProvider(StaticCredentialsProvider.create(credentials))
                    .endpointOverride(URI.create(endpoint))
                    .region(Region.of(region))
                    .build();

            // Upload the file
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(fileName)
                    .contentType("image/jpeg")
                    .build();

            s3Client.putObject(putObjectRequest, RequestBody.fromBytes(fileBytes));

            // Return the public URL
            return "https://tovaxwkyjnrfssqzpmcl.supabase.co/storage/v1/object/public/" + bucketName + "/" + fileName;

        } catch (Exception e) {
            throw new RuntimeException("Failed to upload screenshot to Supabase storage: " + e.getMessage(), e);
        }
    }

    /**
     * Generate a unique filename for screenshots organized by user ID
     * @param userId The user ID to organize files by user
     * @param url The original URL being captured
     * @return A unique filename with user ID, URL hash, and timestamp
     */
    private String generateScreenshotFileName(String userId, String url) {
        try {
            // Create hash of URL for filename
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(url.getBytes());
            StringBuilder hashString = new StringBuilder();
            for (byte b : hashBytes) {
                hashString.append(String.format("%02x", b));
            }

            // Generate timestamp
            long timestamp = Instant.now().toEpochMilli();

            // Return user-organized path: userId/urlHash_timestamp.jpg
            return userId + "/" + hashString.toString() + "_" + timestamp + ".jpg";
        } catch (NoSuchAlgorithmException e) {
            // Fallback to simple timestamp if MD5 fails
            long timestamp = Instant.now().toEpochMilli();
            return userId + "/screenshot_" + timestamp + ".jpg";
        }
    }

    /**
     * Delete a file from Supabase storage
     * @param fileName The name of the file to delete
     * @throws IOException If deletion fails
     */
    public void deleteFile(String fileName) throws IOException {
        String baseUrl = supabaseUrl.replace("/auth/v1", "");
        String deleteUrl = baseUrl + "/storage/v1/object/" + bucketName + "/" + fileName;

        HttpURLConnection connection = (HttpURLConnection) new URL(deleteUrl).openConnection();
        connection.setRequestMethod("DELETE");
        connection.setRequestProperty("Authorization", "Bearer " + supabaseServiceKey);

        int responseCode = connection.getResponseCode();
        if (responseCode != 200 && responseCode != 204) {
            throw new IOException("Failed to delete file from Supabase: " + responseCode);
        }
    }
}
