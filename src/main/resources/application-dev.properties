# Development Environment Configuration
# Use this profile for local development: --spring.profiles.active=dev

spring.application.name=scraper

# ScrapingBee API Configuration
scrapingbee.api.key=********************************************************************************

# Local MongoDB Configuration
spring.data.mongodb.uri=mongodb://localhost:27017/scraping

# Mailjet Configuration
mailjet.api.secret=********************************
mailjet.api.key=********************************
mailjet.api.url=https://api.mailjet.com/v3.1/send

# Google Cloud Vision API Configuration
google.cloud.vision.credentials=************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

# CORS Configuration for local development
spring.web.cors.allowed-origins=https://bargainhawk.ca,http://localhost:5173,http://localhost:5174,http://localhost:5175,http://localhost:5176,http://localhost:5177
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS,PATCH
spring.web.cors.allowed-headers=Content-Type,Accept,Authorization
spring.web.cors.allow-credentials=true

# Supabase Configuration (Development)
supabase.jwt.secret=iVwQfKKcauEr8KCbVLdc/0WP32IhcPm0yCBjKPHE+dpjiC4hMGZ26IlXpFvnR5GpCLgXzrtkueCO+L7p0Ug/tw==
supabase.url=https://tovaxwkyjnrfssqzpmcl.supabase.co/auth/v1
supabase.anon.key=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.04PqXEEbZFyuhJhFV-gfiF1B875KGVgXZHxhV0l7UtA
supabase.project.ref=tovaxwkyjnrfssqzpmcl
supabase.service.key=

# S3 Storage Configuration (Development)
s3.access.key.id=427b0bc78cb33e93214e34f6f70f4be0
s3.secret.access.key=59c8ef78530de3bc7cf7e18e1ec16f7d25353083029d090fcdbf0d14ecd1d627
s3.endpoint=https://tovaxwkyjnrfssqzpmcl.supabase.co/storage/v1/s3
s3.region=ca-central-1
s3.bucket.name=items
