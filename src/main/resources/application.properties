spring.application.name=scraper
scrapingbee.api.key=********************************************************************************
supabase.jwt.secret=HIFZ2SPkJdqm/6AORj6BwpayERtGVhk88MplQDPcbTbF4EsUZwo7oc1R3G8rNY3Us+6oS93V9cpLn6OKXLyaAg==
supabase.url=https://sopqivjtprerkbfsnfna.supabase.co/auth/v1
# spring.data.mongodb.host=localhost
# spring.data.mongodb.port=27017
# spring.data.mongodb.database=scraping
mailjet.api.secret=********************************
mailjet.api.key=********************************
mailjet.api.url=https://api.mailjet.com/v3.1/send
spring.data.mongodb.uri=mongodb+srv://maplecantech:<EMAIL>/scraping
google.cloud.vision.credentials=************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
# Enable CORS globally
spring.web.cors.allowed-origins=https://bargainhawk.ca,http://localhost:5173,http://localhost:5174,http://localhost:5175,http://localhost:5176
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.web.cors.allowed-headers=Content-Type,Accept,Authorization
spring.web.cors.allow-credentials=true

# S3 Storage Configuration
s3.access.key.id=9ee508e627028b97a2b6f8cf47c001e4
s3.secret.access.key=3847b6db9c421ecf020a2274ca0034a87ec4911ff496cf41d980a220baa5e150
s3.endpoint=https://sopqivjtprerkbfsnfna.supabase.co/storage/v1/s3
s3.region=ca-central-1
s3.bucket.name=items

##dev
#supabase.jwt.secret=iVwQfKKcauEr8KCbVLdc/0WP32IhcPm0yCBjKPHE+dpjiC4hMGZ26IlXpFvnR5GpCLgXzrtkueCO+L7p0Ug/tw==
#supabase.url=https://tovaxwkyjnrfssqzpmcl.supabase.co/auth/v1
#
## S3 Storage Configuration
#s3.access.key.id=427b0bc78cb33e93214e34f6f70f4be0
#s3.secret.access.key=59c8ef78530de3bc7cf7e18e1ec16f7d25353083029d090fcdbf0d14ecd1d627
#s3.endpoint=https://tovaxwkyjnrfssqzpmcl.supabase.co/storage/v1/s3
#s3.region=ca-central-1
#s3.bucket.name=items