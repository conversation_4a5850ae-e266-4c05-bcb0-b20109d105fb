package com.maplecan.scraper.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.maplecan.scraper.configuration.UserPrincipal;
import com.maplecan.scraper.model.UserMetadata;
import com.maplecan.scraper.service.UnsubscribeService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.security.core.Authentication;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
class AccountControllerTest {

    @Mock
    private UnsubscribeService unsubscribeService;

    @Mock
    private Authentication authentication;

    @InjectMocks
    private AccountController accountController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;
    private UserPrincipal userPrincipal;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(accountController).build();
        objectMapper = new ObjectMapper();
        userPrincipal = new UserPrincipal("<EMAIL>", "user123");
        when(authentication.getPrincipal()).thenReturn(userPrincipal);
    }

    @Test
    void testGetUserInfo_ExistingUser() throws Exception {
        // Arrange
        UserMetadata userMetadata = UserMetadata.builder()
                .email("<EMAIL>")
                .firstName("John")
                .lastName("Doe")
                .emailSubscription(true)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        when(unsubscribeService.getUserMetadata("<EMAIL>"))
                .thenReturn(Optional.of(userMetadata));

        // Act & Assert
        mockMvc.perform(get("/api/account/info")
                        .principal(authentication))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.firstName").value("John"))
                .andExpect(jsonPath("$.lastName").value("Doe"))
                .andExpect(jsonPath("$.emailSubscription").value(true));

        verify(unsubscribeService).getUserMetadata("<EMAIL>");
    }

    @Test
    void testGetUserInfo_NewUser() throws Exception {
        // Arrange
        when(unsubscribeService.getUserMetadata("<EMAIL>"))
                .thenReturn(Optional.empty());

        // Act & Assert
        mockMvc.perform(get("/api/account/info")
                        .principal(authentication))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.firstName").value(""))
                .andExpect(jsonPath("$.lastName").value(""))
                .andExpect(jsonPath("$.emailSubscription").value(true));

        verify(unsubscribeService).getUserMetadata("<EMAIL>");
    }

    @Test
    void testUpdateUserInfo_Success() throws Exception {
        // Arrange
        Map<String, String> request = Map.of(
                "firstName", "John",
                "lastName", "Doe"
        );

        when(unsubscribeService.updateUserInfo("<EMAIL>", "John", "Doe"))
                .thenReturn(true);

        // Act & Assert
        mockMvc.perform(post("/api/account/update-info")
                        .principal(authentication)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Personal information updated successfully"));

        verify(unsubscribeService).updateUserInfo("<EMAIL>", "John", "Doe");
    }

    @Test
    void testUpdateUserInfo_FirstNameTooLong() throws Exception {
        // Arrange
        String longName = "a".repeat(51); // 51 characters
        Map<String, String> request = Map.of(
                "firstName", longName,
                "lastName", "Doe"
        );

        // Act & Assert
        mockMvc.perform(post("/api/account/update-info")
                        .principal(authentication)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("First name must be 50 characters or less"));

        verify(unsubscribeService, never()).updateUserInfo(anyString(), anyString(), anyString());
    }

    @Test
    void testUpdateUserInfo_LastNameTooLong() throws Exception {
        // Arrange
        String longName = "a".repeat(51); // 51 characters
        Map<String, String> request = Map.of(
                "firstName", "John",
                "lastName", longName
        );

        // Act & Assert
        mockMvc.perform(post("/api/account/update-info")
                        .principal(authentication)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("Last name must be 50 characters or less"));

        verify(unsubscribeService, never()).updateUserInfo(anyString(), anyString(), anyString());
    }

    @Test
    void testUpdateUserInfo_ServiceFailure() throws Exception {
        // Arrange
        Map<String, String> request = Map.of(
                "firstName", "John",
                "lastName", "Doe"
        );

        when(unsubscribeService.updateUserInfo("<EMAIL>", "John", "Doe"))
                .thenReturn(false);

        // Act & Assert
        mockMvc.perform(post("/api/account/update-info")
                        .principal(authentication)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("Failed to update personal information"));

        verify(unsubscribeService).updateUserInfo("<EMAIL>", "John", "Doe");
    }

    @Test
    void testUpdateEmailSubscription_EnableNotifications() throws Exception {
        // Arrange
        Map<String, Boolean> request = Map.of("emailSubscription", true);

        when(unsubscribeService.updateEmailSubscription("<EMAIL>", true))
                .thenReturn(true);

        // Act & Assert
        mockMvc.perform(post("/api/account/update-subscription")
                        .principal(authentication)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Email notifications enabled successfully"))
                .andExpect(jsonPath("$.emailSubscription").value(true));

        verify(unsubscribeService).updateEmailSubscription("<EMAIL>", true);
    }

    @Test
    void testUpdateEmailSubscription_DisableNotifications() throws Exception {
        // Arrange
        Map<String, Boolean> request = Map.of("emailSubscription", false);

        when(unsubscribeService.updateEmailSubscription("<EMAIL>", false))
                .thenReturn(true);

        // Act & Assert
        mockMvc.perform(post("/api/account/update-subscription")
                        .principal(authentication)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Email notifications disabled successfully"))
                .andExpect(jsonPath("$.emailSubscription").value(false));

        verify(unsubscribeService).updateEmailSubscription("<EMAIL>", false);
    }

    @Test
    void testUpdateEmailSubscription_MissingParameter() throws Exception {
        // Arrange - Send empty JSON object (no emailSubscription field)
        String emptyRequest = "{}";

        // Act & Assert
        mockMvc.perform(post("/api/account/update-subscription")
                        .principal(authentication)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(emptyRequest))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("Email subscription preference is required"));

        verify(unsubscribeService, never()).updateEmailSubscription(anyString(), anyBoolean());
    }

    @Test
    void testUpdateEmailSubscription_ServiceFailure() throws Exception {
        // Arrange
        Map<String, Boolean> request = Map.of("emailSubscription", true);

        when(unsubscribeService.updateEmailSubscription("<EMAIL>", true))
                .thenReturn(false);

        // Act & Assert
        mockMvc.perform(post("/api/account/update-subscription")
                        .principal(authentication)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("Failed to update email subscription preference"));

        verify(unsubscribeService).updateEmailSubscription("<EMAIL>", true);
    }
}
